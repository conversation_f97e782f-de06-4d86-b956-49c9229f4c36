import React from "react";
import ScrollAnimation from "react-animate-on-scroll";

interface ScrollAnimProps {
    noAnim?: boolean,
    delay?: number,
    duration?: number,
	direction?: 'top' | 'bottom' | 'left' | 'right',
    className?: string,
}

function slideDirectionClass(direction: 'top' | 'bottom' | 'left' | 'right') {
	switch (direction) {
		case 'top':
			return 'slide-in-from-top';
		case 'bottom':
			return 'slide-in-from-bottom';
		case 'left':
			return 'slide-in-from-left';
		case 'right':
			return 'slide-in-from-right';
		default:
			return 'slide-in-from-bottom';
	}
}

const ScrollAnim: React.FC<React.PropsWithChildren<ScrollAnimProps>> = ({ noAnim = false, delay = 0, duration, direction='bottom', className, children }) => (
    noAnim ? <>{children}</> :
    <ScrollAnimation className={className} animateIn={`fade-in ${slideDirectionClass(direction)} animate-in`} animateOnce delay={delay} duration={duration}>
        {children}
    </ScrollAnimation>
);

export default ScrollAnim;