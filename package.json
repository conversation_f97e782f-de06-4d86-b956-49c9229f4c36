{"name": "mbs", "version": "1.0.0", "private": true, "description": "mbs", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "keywords": ["gatsby"], "scripts": {"develop": "gatsby develop", "start": "gatsby develop", "build": "gatsby build", "serve": "gatsby serve", "clean": "gatsby clean", "typecheck": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@portabletext/react": "^3.1.0", "@portabletext/to-html": "^2.0.13", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@radix-ui/react-visually-hidden": "^1.1.2", "@tailwindcss/container-queries": "^0.1.1", "@types/webpack-env": "^1.18.5", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^3.6.0", "embla-carousel-auto-scroll": "^8.3.1", "embla-carousel-autoplay": "^8.3.1", "embla-carousel-react": "^8.3.1", "gatsby": "^5.13.7", "gatsby-plugin-image": "^3.14.0", "gatsby-plugin-layout": "^4.13.1", "gatsby-plugin-manifest": "^5.14.0", "gatsby-plugin-minify": "^0.5.0", "gatsby-plugin-nprogress": "^5.14.0", "gatsby-plugin-postcss": "^6.13.1", "gatsby-plugin-react-svg": "^3.3.0", "gatsby-plugin-robots-txt": "^1.8.0", "gatsby-plugin-sharp": "^5.13.1", "gatsby-plugin-sitemap": "^6.13.1", "gatsby-source-filesystem": "^5.13.1", "gatsby-source-sanity": "^7.9.1", "gatsby-transformer-sharp": "^5.13.1", "input-otp": "^1.4.1", "leaflet": "^1.9.4", "lucide-react": "^0.454.0", "postcss": "^8.4.47", "react": "^18.2.0", "react-animate-on-scroll": "^2.1.9", "react-animated-numbers": "^0.18.0", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-hook-form": "^7.53.1", "react-inlinesvg": "^4.1.5", "react-leaflet": "^4.2.1", "recharts": "^2.13.3", "svg-react-loader": "^0.4.6", "tailwind-merge": "^2.5.4", "tailwindcss": "^3.4.14", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.1", "zod": "^3.23.8"}, "devDependencies": {"@types/leaflet": "^1.9.14", "@types/node": "^20.11.19", "@types/react": "^18.2.55", "@types/react-animate-on-scroll": "^2.1.8", "@types/react-dom": "^18.2.19", "gatsby-plugin-root-import": "^2.0.9", "typescript": "^5.3.3", "webpack": "^5.96.1"}, "packageManager": "pnpm@9.15.3+sha512.1f79bc245a66eb0b07c5d4d83131240774642caaa86ef7d0434ab47c0d16f66b04e21e0c086eb61e62c77efc4d7f7ec071afad3796af64892fae66509173893a"}