import BigList from "@/components/sections/bigList";
import CarouselSection from "@/components/sections/Carousel";
import Columns from "@/components/sections/column";
import DefaultContent from "@/components/sections/content";
import DynamicForm from "@/components/sections/dynamicForm";
import Employees from "@/components/sections/Employees";
import IconCards from "@/components/sections/iconCards";
import Icons from "@/components/sections/icons";
import Offices from "@/components/sections/offices";
import Product from "@/components/sections/product";
import { ContentHandlerProps } from "@/components/sections/sanityTypes";
import TabsSection from "@/components/sections/tabs";
import Timeline from "@/components/sections/Timeline";
import React from "react";

const ContentHandler: React.FC<ContentHandlerProps> = ({ type, content, carousel, iconCards, icons, tabs, bigList, product, columns, employees, offices, timeline, form: dynamicForm }: ContentHandlerProps) => {
    switch (type) {
        case 'Carousel':
            return carousel && <CarouselSection {...carousel} />
        case 'Icons':
            return icons && icons.length > 0 && <Icons icons={icons} />
        case 'Tabs':
            return tabs && tabs.length > 0 && <TabsSection tabs={tabs} />
        case 'Product':
            return product && <Product {...product} />
        case 'Big List':
            return bigList && bigList.length > 0 && <BigList list={bigList} />
        case 'Icon Cards':
            return iconCards && iconCards.length > 0 && <IconCards iconCards={iconCards} />
        case 'Columns':
            return columns && <Columns {...columns} />
        case 'Employees':
            return employees && employees.length > 0 && <Employees employees={employees} />
        case 'Offices':
            return offices && offices.length > 0 && <Offices offices={offices} />
		case 'Timeline':
			return timeline && timeline.length > 0 && <Timeline timeline={timeline} />
		case 'Form':
			return dynamicForm && <DynamicForm {...dynamicForm} />
        default:
            return <>
                {content?.map((item, index) => <DefaultContent key={index} {...item} />)}
            </>
    }
}

export default ContentHandler