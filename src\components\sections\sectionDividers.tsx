import React from 'react';

export const topCurvePath = "M0,86 L48,86 C96,86,192,86,288,75.3 C384,65,480,43,576,27.3 C672,11,768,1,864,6 C960,11,1056,33,1152,38 C1248,43,1344,33,1392,27.3 L1440,22 L1440,224 L0,224 Z"
export const bottomCurvePath = "M0,0L0,224L48,224C96,224,192,224,288,213.3C384,203,480,181,576,165.3C672,149,768,139,864,144C960,149,1056,171,1152,176C1248,181,1344,171,1392,165.3L1440,160L1440,0L0,0Z"

const dividerPath = "M0,86 L48,86 C96,86,192,86,288,75.3 C384,65,480,43,576,27.3 C672,11,768,1,864,6 C960,11,1056,33,1152,38 C1248,43,1344,33,1392,27.3 L1440,22"

export const SectionDivider = () => {
    return (
        <div className="w-full">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 90" className="w-full block relative text-border">
                <path
                    className='translate-y-0.5'
                    fill="none"
                    stroke="currentColor"
                    strokeWidth={2}
                    d={dividerPath}
                />
                <path
                    className='-translate-y-0.5'
                    fill="none"
                    stroke="currentColor"
                    strokeWidth={2}
                    d={dividerPath}
                />
            </svg>
        </div >
    )
}

export const SectionUpper: React.FC = () => (
    <div className="w-full -z-10 relative md:-mb-[5%]">
        <svg
            id='topWave'
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 1440 224"
            className="w-full h-full text-foreground translate-y-1"
            preserveAspectRatio="none"
        >
            <path
                fill="currentColor"
                fillOpacity="1"
                d={topCurvePath}
            />
        </svg>
    </div>
)

export const SectionLower: React.FC<{ bg?: 'default' | 'dark', last?: boolean }> = ({ bg = 'default', last = false }) => (
    <div className={`w-full ${last ? "bg-slate-100" : ""} -z-10 relative -mt-[5%]`}>
        <svg
            id='bottomWave'
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 1440 224"
            className={`w-full h-full block -translate-y-1 ${bg === 'dark' ? "text-foreground" : "text-background"}`}
            preserveAspectRatio="none"
        >
            <path
                fill="currentColor"
                fillOpacity="1"
                d={bottomCurvePath}
            />
        </svg>
    </div>
)