import { DefaultContentProps } from "@/components/sections/sanityTypes";
import { useSection } from "@/components/sections/section";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import ScrollAnim from "@/components/ui/scrollAnim";
import { Separator } from "@/components/ui/separator";
import { Typography } from "@/components/ui/typography";
import sanitySerializers from "@/lib/sanity";
import { splitStringInRatio } from "@/lib/utils";
import { PortableText } from "@portabletext/react";
import { Link } from "gatsby";
import { GatsbyImage } from "gatsby-plugin-image";
import React from "react";
import SVG from "react-inlinesvg";

interface DefaultContentPropsWithStack extends DefaultContentProps {
	stacked?: boolean;
}

const DefaultContent: React.FC<DefaultContentPropsWithStack> = ({ title, cta, ctaIcon, ctaLink, content, image, stacked = false, imageAsBg = false, imageAlign }) => {
	const { bg, hasTitle } = useSection();
	const cardShadow = bg === "dark" ? "shadow-slate-950 shadow-xl" : "";
	const cardTransparent = bg === "dark" ? "border-none bg-transparent dark shadow-none" : "";
	const { part1: title1, part2: title2 } = splitStringInRatio(title);

	const imageAlt = image?.alt || title || (content?.length > 0 && content[0].children[0].text) || "image";
	const imageTitle = image?.title || title || (content?.length > 0 && content[0].children[0].text) || "image";

	const ctaIsInternal = ctaLink?.startsWith("/");
	const CTAWrapper = ({ children }: { children: React.JSX.Element }) =>
		ctaIsInternal ? (
			<Link to={ctaLink} className="w-fit">
				{children}
			</Link>
		) : (
			<a href={ctaLink}>{children}</a>
		);

	if (!content && !title && !cta)
		return (
			<ScrollAnim className="h-full">
				<Card className="h-full flex flex-col border-none">
					{title && (
						<CardHeader className="pb-6">
							<CardTitle>
								<Typography size="xl" component={hasTitle ? "h3" : "h2"}>
									{title}
								</Typography>
							</CardTitle>
						</CardHeader>
					)}
					<div className="flex-1">
						<GatsbyImage
							image={image.asset.gatsbyImageData}
							alt={imageAlt}
							title={imageTitle}
							className="rounded-lg h-full w-full"
							objectFit="cover"
							objectPosition={imageAlign && `object-${imageAlign}`}
						/>
					</div>
				</Card>
			</ScrollAnim>
		);
	else if (imageAsBg) {
		const contentTruncated = content[0]?.children[0]?.text?.slice(0, 110) + (content[0]?.children[0]?.text?.length > 110 ? "..." : "");
		return (
			<ScrollAnim className="h-full group/card">
				<Card className="shadow-md w-full h-full flex justify-center items-end relative">
					{/* img */}
					{image?.asset && (
						<GatsbyImage
							image={image.asset.gatsbyImageData}
							alt={imageAlt}
							title={imageTitle}
							className="!absolute h-full w-full rounded-lg group-hover:scale-105 ease-in-out transition-all duration-300"
							objectPosition={imageAlign && `object-${imageAlign}`}
						/>
					)}

					{/* Vigniette */}
					<div
						className="absolute h-full w-full bg-gradient-to-t from-slate-950
                        via-slate-900 to-transparent rounded-lg opacity-90"
					/>

					<CardContent className="w-full p-5 lg:p-10 mt-auto text-muted flex flex-col justify-between gap-4 drop-shadow-outline">
						<div>
							<Typography component={hasTitle ? "h3" : "h2"} size="xxl">
								{title1} <span className="text-secondary">{title2}</span>
							</Typography>
							<Separator className="mt-4" />
						</div>

						{contentTruncated && (
							<>
								<Typography weight="light" className="group-hover/card:hidden opacity-100 group-hover/card:opacity-0 transition-opacity duration-300">
									{contentTruncated}
								</Typography>

								{/* Full content - hidden by default, shown on hover */}
								<div className="hidden group-hover/card:block opacity-0 group-hover/card:opacity-100 transition-opacity duration-300">
									<PortableText value={content} components={sanitySerializers("font-light mb-4 last:mb-0")} />
								</div>

								<span className="sr-only">
									<PortableText value={content} components={sanitySerializers()} />
								</span>
							</>
						)}

						{cta && (
							<CTAWrapper>
								<Button variant="outline">{cta}</Button>
							</CTAWrapper>
						)}
					</CardContent>
				</Card>
			</ScrollAnim>
		);
	} else if (stacked)
		return (
			<ScrollAnim className="flex flex-col-reverse relative items-center h-full">
				<Card className={`w-full md:p-4 h-full ${cardShadow} -mt-4`}>
					<CardHeader>
						<CardTitle>
							<Typography size="xl" component={hasTitle ? "h3" : "h2"}>
								{title}
							</Typography>
						</CardTitle>
					</CardHeader>
					<CardContent>
						<CardDescription>{content && <PortableText value={content} components={sanitySerializers("mb-4 last:mb-0")} />}</CardDescription>
					</CardContent>
					{cta && (
						<CardFooter>
							<CTAWrapper>
								<Button>
									{cta} {ctaIcon && <SVG src={ctaIcon.svg} className="size-5" style={{}} />}
								</Button>
							</CTAWrapper>
						</CardFooter>
					)}
				</Card>

				{image?.asset?.gatsbyImageData && (
					<div className="relative w-full aspect-video shrink-0 -z-10">
						<GatsbyImage image={image.asset.gatsbyImageData} alt={imageAlt} title={imageTitle} className="!absolute h-full w-full rounded-lg" objectPosition={"bottom"} />
					</div>
				)}
			</ScrollAnim>
		);
	else if (image?.asset?.gatsbyImageData)
		return (
			<div className={`max-w-screen-xl mx-auto grid grid-cols-1 gap-4 ${image?.asset?.gatsbyImageData && "md:grid-cols-2"} md:items-center md:gap-8 h-full`}>
				<ScrollAnim>
					<Card className={`h-full ${cardTransparent}`}>
						{title && (
							<CardHeader>
								<CardTitle>
									<Typography size={hasTitle ? "xl" : "xxl"} component={hasTitle ? "h3" : "h2"}>
										{title1} <span className="text-secondary">{title2}</span>
									</Typography>
								</CardTitle>
							</CardHeader>
						)}
						<CardContent className={title ? "" : "pt-6"}>
							<CardDescription className={`${bg === "dark" && "text-foreground"}`}>
								{content && <PortableText value={content} components={sanitySerializers(`mb-4 last:mb-0 ${bg === "dark" ? "font-extralight" : "font-light"}`)} />}
							</CardDescription>
						</CardContent>
						{cta && (
							<CardFooter>
								<CTAWrapper>
									<Button>
										{cta} {ctaIcon && <SVG src={ctaIcon.svg} className="size-5" style={{}} />}
									</Button>
								</CTAWrapper>
							</CardFooter>
						)}
					</Card>
				</ScrollAnim>

				{image?.asset?.gatsbyImageData && (
					<ScrollAnim>
						<div className="w-full h-full shrink-0 md:flex justify-center items-center rounded-lg">
							<GatsbyImage
								image={image.asset.gatsbyImageData}
								alt={imageAlt}
								title={imageTitle}
								className="rounded-lg w-full h-full aspect-video"
								objectPosition={imageAlign}
								objectFit="contain"
							/>
						</div>
					</ScrollAnim>
				)}
			</div>
		);
	else
		return (
			<ScrollAnim className="h-full">
				<Card className={`h-full ${cardTransparent}`}>
					{title && (
						<CardHeader>
							<CardTitle>
								<Typography size={hasTitle ? "xl" : "xxl"} component={hasTitle ? "h3" : "h2"}>
									{title1} <span className="text-secondary">{title2}</span>
								</Typography>
							</CardTitle>
						</CardHeader>
					)}
					<CardContent className={title ? "" : "pt-6"}>
						<CardDescription className={`${bg === "dark" && "text-foreground"}`}>
							{content && <PortableText value={content} components={sanitySerializers(`mb-4 last:mb-0 ${bg === "dark" ? "font-extralight" : "font-light"}`)} />}
						</CardDescription>
					</CardContent>
					{cta && (
						<CardFooter>
							<CTAWrapper>
								<Button>
									{cta} {ctaIcon && <SVG src={ctaIcon.svg} className="size-5" style={{}} />}
								</Button>
							</CTAWrapper>
						</CardFooter>
					)}
				</Card>
			</ScrollAnim>
		);
};

export default DefaultContent;
