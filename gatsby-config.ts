import type { GatsbyConfig } from "gatsby";

require("dotenv").config({
	path: `.env`,
});

const config: GatsbyConfig = {
	siteMetadata: {
		title: `MBS`,
		siteUrl: `https://manipalbusinesssolutions.com`,
	},
	// More easily incorporate content into your pages through automatic TypeScript type generation and better GraphQL IntelliSense.
	// If you use VSCode you can also use the GraphQL plugin
	// Learn more at: https://gatsby.dev/graphql-typegen
	graphqlTypegen: true,
	plugins: [
		`gatsby-plugin-layout`,
		"gatsby-plugin-postcss",
		"gatsby-plugin-image",
		"gatsby-transformer-sharp",
		"gatsby-plugin-sharp",
		`gatsby-plugin-minify`,
		{
			resolve: "gatsby-source-filesystem",
			options: {
				name: "images",
				path: "./src/images/",
			},
			__key: "images",
		},
		{
			resolve: "gatsby-plugin-manifest",
			options: {
				icon: "src/images/icon.png",
			},
		},
		{
			resolve: "gatsby-plugin-react-svg",
			options: {
				rule: {
					include: /images/,
				},
			},
		},
		{
			resolve: "gatsby-plugin-root-import",
			options: {
				"@": `${__dirname}/src`,
			},
		},
		{
			resolve: `gatsby-source-sanity`,
			options: {
				projectId: `rgew5gg8`,
				dataset: `production`,
				// a token with read permissions is required
				// if you have a private dataset
				token: process.env.SANITY_TOKEN,

				// If the Sanity GraphQL API was deployed using `--tag <name>`,
				// use `graphqlTag` to specify the tag name. Defaults to `default`.
				graphqlTag: "default",
				watchMode: true,

				// for dev
				overlayDrafts: true,
			},
		},
		{
			resolve: "gatsby-plugin-sitemap",
			options: {
				query: `
                {
                  allSanityPage (filter: {seo: {noindex: {ne: true}}}) {
                    nodes {
                      slug {
                        current
                      }
                      _updatedAt
                    }
                  }
                }
              `,
				resolvePages: ({ allSanityPage: { nodes } }: { allSanityPage: { nodes: { slug: { current: string }; _updatedAt: string }[] } }) =>
					nodes.map((node) => ({
						path: `/${node.slug.current}`,
						lastmod: node._updatedAt,
					})),
				resolveSiteUrl: () => "https://manipalbusinesssolutions.com",
				serialize: ({ path, lastmod }: { path: string; lastmod: string }) => ({
					url: path,
					lastmod,
				}),
			},
		},
		{
			resolve: "gatsby-plugin-robots-txt",
			options: {
				host: "https://manipalbusinesssolutions.com",
				sitemap: "https://manipalbusinesssolutions.com/sitemap-index.xml",
				policy: [{ userAgent: "*", allow: "/" }],
			},
		},
	],
};

export default config;
