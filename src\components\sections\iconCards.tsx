import { IconCardProps } from '@/components/sections/sanityTypes';
import { useSection } from '@/components/sections/section';
import { Card } from '@/components/ui/card';
import ScrollAnim from '@/components/ui/scrollAnim';
import { Typography } from '@/components/ui/typography';
import sanitySerializers from '@/lib/sanity';
import { PortableText } from '@portabletext/react';
import { GatsbyImage } from 'gatsby-plugin-image';
import React from 'react';
import SVG from 'react-inlinesvg';

interface IconCardItemProps extends IconCardProps {
    align?: 'left' | 'top';
    minify?: boolean;
    large?: boolean;
}

export const IconCardItem: React.FC<IconCardItemProps> = ({ type, image, icon, title, description, align = 'top', minify = true, large = false }: IconCardItemProps) => {
    const { bg, hasTitle } = useSection();
    const cardShadow = bg === "dark" ? `shadow-slate-950 ${minify ? "shadow-none lg:shadow-xl" : "shadow-xl"}` : `${minify ? "shadow-none lg:shadow-center-xl" : "shadow-center-xl"}`;

    const cardMain = minify ?
        `flex-row ${align === 'top' && !large && "lg:flex-col"} gap-4 ${align === 'top' && !large && "lg:gap-0"} p-2 lg:p-6 border-none lg:border lg:hover:scale-105` :
        `flex-col gap-0 p-6 hover:scale-105`;


    const imgIsSVG = type === 'image' && image?.asset?.publicUrl?.endsWith('.svg');

    return (
        <Card className={`${cardMain} flex h-full ${cardShadow} duration-150`}>
            {
                type === 'image' ? image && (imgIsSVG ?
                    <div className={`${large ? "md:m-auto" : "lg:mx-auto"} text-primary fill-primary shrink-0`}>
                        <SVG
                            src={image.asset.url}
                            title={title}
                            className='size-10 md:size-20 flex-shrink-0 mt-1 lg:mt-0'
                            style={{}}
                        >
                            <img
                                src={image.asset.url}
                                alt={title}
                                className='size-10 md:size-20 flex-shrink-0 mt-1 lg:mt-0'
                            />
                        </SVG>
                    </div> :
                    <div className="lg:mx-auto relative ">
                        <GatsbyImage
                            image={image.asset.gatsbyImageData}
                            alt={image?.alt || title || 'image'}
                            title={image?.title || title || 'image'}
                            objectFit='contain'
                            className="h-10 w-full md:h-20 flex-shrink-0 mt-1 lg:mt-0"
                        />
                    </div>) : icon && <div className='lg:mx-auto text-primary fill-primary'>
                        <SVG
                            src={icon.svg}
                            title={title}
                            className='size-10 md:size-20 flex-shrink-0 mt-1 lg:mt-0'
                            style={{}}
                        />
                    </div>
            }
            <div className={align === 'top' ? `${minify ? large ? "" : "lg:text-center" : "text-center"}  break-words` : "text-wrap"}>
                <Typography component={hasTitle ? "h3" : "h2"} size='xl' className={minify ? "lg:my-2" : "my-2"}>{title}</Typography>
                {description && <PortableText value={description} components={sanitySerializers("font-light mb-2 last:mb-0")} />}
            </div>
        </Card>
    )
}

const IconCards: React.FC<{ iconCards: IconCardProps[] }> = ({ iconCards }) => {
    // Dynamically get columns based on the number of cards
    const cols = iconCards.length % 4 == 0 ? 'lg:grid-cols-8' : 'lg:grid-cols-6';

    const remainder = iconCards.length % 4 == 0 ? 0 : iconCards.length % 3;
    const remLength = iconCards.length - remainder;

    console.log(iconCards)

    const avgLen = iconCards.reduce((acc, curr) => acc + (curr?.description?.[0]?.children?.[0]?.text?.length || 0), 0) / iconCards.length;

    const isLarge = avgLen > 256;

    if (iconCards.length == 1) return (
        <div className={`grid grid-cols-1 ${isLarge ? "" : "lg:grid-cols-3"} gap-4`}>
            <div className='lg:col-start-2'>
                {iconCards.map((card, index) => (
                    <ScrollAnim key={index}>
                        <IconCardItem {...card} />
                    </ScrollAnim>
                ))}
            </div>
        </div>
    )

    if (iconCards.length == 2) return (
        <div className={`grid grid-cols-1 lg:grid-cols-7 gap-4`}>
            {iconCards.map((card, index) => (
                <ScrollAnim className='first:lg:col-start-2 last:lg:col-start-5 lg:col-span-2' key={index}>
                    <IconCardItem {...card} />
                </ScrollAnim>
            ))}
        </div>
    )

    return (
        <div className={`grid grid-cols-1 ${isLarge ? "" : cols} gap-4 px-4`}>
            {iconCards.slice(0, iconCards.length).map((card, index) => (
                index < remLength ?
                    <ScrollAnim className='lg:col-span-2' key={index}>
                        <IconCardItem {...card} large={isLarge} />
                    </ScrollAnim> :
                    remainder == 1 ?
                        <ScrollAnim className='lg:col-start-3 lg:col-span-2' key={index}>
                            <IconCardItem {...card} large={isLarge} />
                        </ScrollAnim> :
                        <ScrollAnim className={`${index == remLength ? "lg:col-start-2" : "lg:col-start-4"} lg:col-span-2`} key={index}>
                            <IconCardItem {...card} large={isLarge} />
                        </ScrollAnim>

            ))}
        </div>
    )
}

export default IconCards