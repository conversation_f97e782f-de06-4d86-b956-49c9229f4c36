import { ListItemProps } from '@/components/sections/sanityTypes';
import { useSection } from '@/components/sections/section';
import ScrollAnim from '@/components/ui/scrollAnim';
import { Typography } from '@/components/ui/typography';
import sanitySerializers from '@/lib/sanity';
import { PortableText } from '@portabletext/react';
import { CheckCircle } from 'lucide-react';
import React, { useEffect, useState } from 'react';

const BigList: React.FC<{ list: ListItemProps[] }> = ({ list }) => {
    const { hasTitle } = useSection();
    return (
        <ul className={`columns-1 ${list.length > 3 ? "md:columns-2" : ""}`}>
            {list.map((item, index) => (
                <ScrollAnim key={index}>
                    <li className="flex items-start space-x-6 mr-8 mb-4">
                        <CheckCircle className="text-secondary h-10 w-10 shrink-0" />
                        <div>
                            <Typography size="xl" component={hasTitle ? "h3" : "h2"}>
                                {item.title}
                            </Typography>
                            <PortableText value={item.description} components={sanitySerializers(`mt-2`)}/>
                        </div>
                    </li>
                </ScrollAnim>
            ))}
        </ul>
    )
}

export default BigList;