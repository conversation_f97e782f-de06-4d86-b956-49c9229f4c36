import { writeFileSync } from "fs";
import type { GatsbyNode } from "gatsby";
import * as path from "path";

type hsl = {
    a: number;
    h: number;
    l: number;
    s: number;
}

type rawTheme = {
    background: { hsl: hsl };
    foreground: { hsl: hsl };
    foregroundText: { hsl: hsl };
    card: { hsl: hsl };
    cardForeground: { hsl: hsl };
    popover: { hsl: hsl };
    popoverForeground: { hsl: hsl };
    primary: { hsl: hsl };
    primaryForeground: { hsl: hsl };
    secondary: { hsl: hsl };
    secondaryForeground: { hsl: hsl };
    muted: { hsl: hsl };
    mutedForeground: { hsl: hsl };
    accent: { hsl: hsl };
    accentForeground: { hsl: hsl };
    destructive: { hsl: hsl };
    destructiveForeground: { hsl: hsl };
    border: { hsl: hsl };
    input: { hsl: hsl };
    ring: { hsl: hsl };
    radius: string;
    chart1: { hsl: hsl };
    chart2: { hsl: hsl };
    chart3: { hsl: hsl };
    chart4: { hsl: hsl };
    chart5: { hsl: hsl };
    sidebarBackground: { hsl: hsl };
    sidebarForeground: { hsl: hsl };
    sidebarPrimary: { hsl: hsl };
    sidebarPrimaryForeground: { hsl: hsl };
    sidebarAccent: { hsl: hsl };
    sidebarAccentForeground: { hsl: hsl };
    sidebarBorder: { hsl: hsl };
    sidebarRing: { hsl: hsl };
}

type theme = {
    background: string;
    foreground: string;
    foregroundText: string;
    card: string;
    cardForeground: string;
    popover: string;
    popoverForeground: string;
    primary: string;
    primaryForeground: string;
    secondary: string;
    secondaryForeground: string;
    muted: string;
    mutedForeground: string;
    accent: string;
    accentForeground: string;
    destructive: string;
    destructiveForeground: string;
    border: string;
    input: string;
    ring: string;
    radius: string;
    chart1: string;
    chart2: string;
    chart3: string;
    chart4: string;
    chart5: string;
    sidebarBackground: string;
    sidebarForeground: string;
    sidebarPrimary: string;
    sidebarPrimaryForeground: string;
    sidebarAccent: string;
    sidebarAccentForeground: string;
    sidebarBorder: string;
    sidebarRing: string;
}

type result = {
    result: rawTheme
}

function rawHSLToString(hsl: hsl): string {
    if (hsl)
        return `${hsl.h} ${hsl.s * 100}% ${hsl.l * 100}%`;
    return '';
}

async function SaveThemeInCache(reporter: any) {
    const endpoint = 'https://rgew5gg8.api.sanity.io/v2022-03-07/data/query/production?query=*%5B_type+%3D%3D+"Theme"%5D%5B0%5D';
    reporter.info("Fetching theme themes...");

    const response = await fetch(endpoint);
    const themes: result = await response.json();

    // Assuming themes.rootVars and themes.darkVars contain the CSS
    if (themes) {
        reporter.info("Theme data fetched successfully.");
    } else {
        reporter.warn("No theme data available.");
    }

    const theme: theme = {
        background: rawHSLToString(themes?.result?.background.hsl),
        foreground: rawHSLToString(themes?.result?.foreground.hsl),
        foregroundText: rawHSLToString(themes?.result?.foregroundText.hsl),
        card: rawHSLToString(themes?.result?.card.hsl),
        cardForeground: rawHSLToString(themes?.result?.cardForeground.hsl),
        popover: rawHSLToString(themes?.result?.popover.hsl),
        popoverForeground: rawHSLToString(themes?.result?.popoverForeground.hsl),
        primary: rawHSLToString(themes?.result?.primary.hsl),
        primaryForeground: rawHSLToString(themes?.result?.primaryForeground.hsl),
        secondary: rawHSLToString(themes?.result?.secondary.hsl),
        secondaryForeground: rawHSLToString(themes?.result?.secondaryForeground.hsl),
        muted: rawHSLToString(themes?.result?.muted.hsl),
        mutedForeground: rawHSLToString(themes?.result?.mutedForeground.hsl),
        accent: rawHSLToString(themes?.result?.accent.hsl),
        accentForeground: rawHSLToString(themes?.result?.accentForeground.hsl),
        destructive: rawHSLToString(themes?.result?.destructive.hsl),
        destructiveForeground: rawHSLToString(themes?.result?.destructiveForeground.hsl),
        border: rawHSLToString(themes?.result?.border.hsl),
        input: rawHSLToString(themes?.result?.input.hsl),
        ring: rawHSLToString(themes?.result?.ring.hsl),
        radius: themes?.result?.radius,
        chart1: rawHSLToString(themes?.result?.chart1.hsl),
        chart2: rawHSLToString(themes?.result?.chart2.hsl),
        chart3: rawHSLToString(themes?.result?.chart3.hsl),
        chart4: rawHSLToString(themes?.result?.chart4.hsl),
        chart5: rawHSLToString(themes?.result?.chart5.hsl),
        sidebarBackground: rawHSLToString(themes?.result?.sidebarBackground.hsl),
        sidebarForeground: rawHSLToString(themes?.result?.sidebarForeground.hsl),
        sidebarPrimary: rawHSLToString(themes?.result?.sidebarPrimary.hsl),
        sidebarPrimaryForeground: rawHSLToString(themes?.result?.sidebarPrimaryForeground.hsl),
        sidebarAccent: rawHSLToString(themes?.result?.sidebarAccent.hsl),
        sidebarAccentForeground: rawHSLToString(themes?.result?.sidebarAccentForeground.hsl),
        sidebarBorder: rawHSLToString(themes?.result?.sidebarBorder.hsl),
        sidebarRing: rawHSLToString(themes?.result?.sidebarRing.hsl),
    }

    const rootVars = `
        :root {
            --background: ${theme.background || "0 0% 100%"};
            --foreground: ${theme.foreground || "217.2 32.6% 17.5%"};
            --foreground-text: ${theme.foregroundText || "217 5% 65%"};
            --card: ${theme.card || "0 0% 100%"};
            --card-foreground: ${theme.cardForeground || "217.2 32.6% 17.5%"};
            --popover: ${theme.popover || "0 0% 100%"};
            --popover-foreground: ${theme.popoverForeground || "217.2 32.6% 17.5%"};
            --primary: ${theme.primary || "359 74% 51%"};
            --primary-foreground: ${theme.primaryForeground || "0 85.7% 97.3%"};
            --secondary: ${theme.secondary || "207 75% 53%"};
            --secondary-foreground: ${theme.secondaryForeground || "0 0% 100%"};
            --muted: ${theme.muted || "0 0% 96.1%"};
            --muted-foreground: ${theme.mutedForeground || "215.4 16.3% 46.9%"};
            --accent: ${theme.accent || "0 0% 96.1%"};
            --accent-foreground: ${theme.accentForeground || "0 0% 9%"};
            --destructive: ${theme.destructive || "0 84.2% 60.2%"};
            --destructive-foreground: ${theme.destructiveForeground || "0 0% 98%"};
            --border: ${theme.border || "0 0% 89.8%"};
            --input: ${theme.input || "0 0% 89.8%"};
            --ring: ${theme.ring || "359 74% 51%"};
            --radius: ${theme.radius || "0.5rem"};
            --chart-1: ${theme.chart1 || "12 76% 61%"};
            --chart-2: ${theme.chart2 || "173 58% 39%"};
            --chart-3: ${theme.chart3 || "197 37% 24%"};
            --chart-4: ${theme.chart4 || "43 74% 66%"};
            --chart-5: ${theme.chart5 || "27 87% 67%"};
            --sidebar-background: ${theme.sidebarBackground || "0 0% 98%"};
            --sidebar-foreground: ${theme.sidebarForeground || "240 5.3% 26.1%"};
            --sidebar-primary: ${theme.sidebarPrimary || "240 5.9% 10%"};
            --sidebar-primary-foreground: ${theme.sidebarPrimaryForeground || "0 0% 98%"};
            --sidebar-accent: ${theme.sidebarAccent || "240 4.8% 95.9%"};
            --sidebar-accent-foreground: ${theme.sidebarAccentForeground || "240 5.9% 10%"};
            --sidebar-border: ${theme.sidebarBorder || "220 13% 91%"};
            --sidebar-ring: ${theme.sidebarRing || "217.2 91.2% 59.8%"};
        }
    `;

    // dark selector
    const darkVars = `
        .dark {
            --background: ${theme.foreground || "217.2 32.6% 17.5%"};
            --foreground: ${theme.muted || "0 0% 96.1%"};
            --card: ${theme.cardForeground || "217.2 32.6% 17.5%"};
            --card-foreground: ${theme.muted || "0 0% 96.1%"};
            --popover: 0 0% 3.9%;
            --popover-foreground: ${theme.muted || "0 0% 96.1%"};
            --primary: ${theme.primary || "359 74% 51%"};
            --primary-foreground: ${theme.primaryForeground || "0 85.7% 97.3%"};
            --secondary: ${theme.secondary || "207 75% 53%"};
            --secondary-foreground: ${theme.secondaryForeground || "0 0% 100%"};
            --muted: 0 0% 14.9%;
            --muted-foreground: ${theme.foregroundText || "217 5% 65%"};
            --accent: 0 0% 14.9%;
            --accent-foreground: ${theme.muted || "0 0% 96.1%"};
            --destructive: 0 62.8% 30.6%;
            --destructive-foreground: ${theme.muted || "0 0% 96.1%"};
            --border: 0 0% 14.9%;
            --input: 0 0% 14.9%;
            --ring: 0 0% 83.1%;
            --chart-1: 220 70% 50%;
            --chart-2: 160 60% 45%;
            --chart-3: 30 80% 55%;
            --chart-4: 280 65% 60%;
            --chart-5: 340 75% 55%;
            --sidebar-background: 240 5.9% 10%;
            --sidebar-foreground: 240 4.8% 95.9%;
            --sidebar-primary: 224.3 76.3% 48%;
            --sidebar-primary-foreground: 0 0% 100%;
            --sidebar-accent: 240 3.7% 15.9%;
            --sidebar-accent-foreground: 240 4.8% 95.9%;
            --sidebar-border: 240 3.7% 15.9%;
            --sidebar-ring: 217.2 91.2% 59.8%;
        }
    `;
    
    // cache data in file
    const rootCache = path.join(process.cwd(), ".cache", "theme-root.txt");
    writeFileSync(rootCache, rootVars, "utf-8");
    reporter.info(`Wrote root theme data to ${rootCache}`);

    const darkCache = path.join(process.cwd(), ".cache", "theme-dark.txt");
    writeFileSync(darkCache, darkVars, "utf-8");
    reporter.info(`Wrote dark theme data to ${darkCache}`);
}

export const onCreateWebpackConfig: GatsbyNode["onCreateWebpackConfig"] = ({ actions }) => {
    actions.setWebpackConfig({
        resolve: {
            alias: {
                "@/components": path.resolve(__dirname, "src/components"),
                "@/lib/utils": path.resolve(__dirname, "src/lib/utils"),
            },
        },
    })
}

export const onPreInit: GatsbyNode["onPreInit"] = async ({ reporter }) => {
    SaveThemeInCache(reporter);
};

export const onCreateDevServer: GatsbyNode["onCreateDevServer"] = async ({ reporter }) => {
    SaveThemeInCache(reporter);
}

const slugExceptions = [
    'home',
    'news',
    'get-in-touch',
    'book-demo',
    'videos',
    'blogs',
]

type slugQuery = {
    allSanityPage: {
        nodes: {
            slug: {
                current: string
            }
        }[]
    },
    allSanityBlog: {
        nodes: {
            slug: {
                current: string;
            }
        }[]
    }
}

export const createPages: GatsbyNode["createPages"] = async ({ actions, graphql }) => {
    const { data } = await graphql(`
        query SlugQuery {
            allSanityPage {
                nodes {
                    slug { current }
                }
            }
            allSanityBlog {
                nodes {
                    slug { current }
                }
            }
        }
    `);

    const pageSlugs = (data as slugQuery).allSanityPage.nodes.map(({ slug }) => slug.current);
    const filteredSlugs = pageSlugs.filter(slug => !slugExceptions.includes(slug));

    filteredSlugs.forEach(slug => {
        actions.createPage({
            path: slug,
            component: path.resolve('./src/templates/page.tsx'),
            context: {
                slug: slug
            }
        })
    });

    const blogSlugs = (data as slugQuery).allSanityBlog.nodes.map(({ slug }) => slug.current);
    blogSlugs.forEach(slug => {
        actions.createPage({
            path: slug,
            component: path.resolve('./src/templates/blog.tsx'),
            context: {
                slug: slug
            }
        })
    });
}
