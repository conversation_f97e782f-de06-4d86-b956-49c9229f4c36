import { ProductProps } from '@/components/sections/sanityTypes';
import { useSection } from '@/components/sections/section';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Typography } from '@/components/ui/typography';
import { splitStringInRatio } from '@/lib/utils';
import { Separator } from '@radix-ui/react-separator';
import { GatsbyImage } from 'gatsby-plugin-image';
import { ChevronRight } from 'lucide-react';
import React, { useState, useEffect } from 'react';
import SVG from 'react-inlinesvg';

const Product: React.FC<ProductProps> = ({ title, description, highlights, image, cta, ctaIcon, ctaLink }: ProductProps) => {
    const { hasTitle } = useSection();
    
    const [titleStart, setTitleStart] = useState<string>("")
    const [titleEnd, setTitleEnd] = useState<string>("")

    useEffect(() => {
        const {part1, part2} = splitStringInRatio(title);
        setTitleStart(part1);
        setTitleEnd(part2);
    }, [title]);
    return (
        <div className="flex flex-col-reverse lg:flex-row lg:gap-8">
            <Card className={`lg:w-${description.length > 0 ? "1/3" : "2/5"} z-[1]`}>
                <CardHeader>
                    <CardTitle>
                        <Typography component={hasTitle ? "h3" : "h2"} size='xxl'>Highlights</Typography>
                    </CardTitle>
                </CardHeader>

                <CardContent>
                    <CardDescription>
                        <ul className="ml-5 list-disc text-muted-foreground">
                            {highlights.map((highlight, index) => (
                                <Typography key={index} component='li'>
                                    {highlight.children[0].text}
                                </Typography>
                            ))}
                        </ul>
                    </CardDescription>
                </CardContent>
            </Card> 

            <Card className={`lg:w-${description.length > 0 ? "2/3" : "1/2"} flex flex-col justify-end relative translate-y-4 lg:translate-y-0`}>
                {/* img */}
                <GatsbyImage
                    image={image.asset.gatsbyImageData}
                    alt={image?.alt || title || description[0].children[0].text || 'image'}
                    title={image?.title || title || description[0].children[0].text || 'image'}
                    className="!absolute h-full w-full rounded-lg"
                />

                {/* Vigniette */}
                {description.length > 0 && <div className="absolute h-full w-full bg-gradient-to-t from-slate-900 to-transparent to-75% rounded-lg" />}

                <CardContent className="pt-6 text-muted flex flex-col justify-between gap-4 drop-shadow-outline pb-10 lg:pb-6">
                    <div>
                        <Typography component={hasTitle ? "h3" : "h2"} size='xl3'>{titleStart} <span className="text-secondary">{titleEnd}</span></Typography>
                        <Separator className="mt-4" />
                    </div>

                    {description.map((desc, index) => (
                        <Typography key={index} className='mb-4 last:mb-0' weight='light'>
                            {desc.children[0].text}
                        </Typography>
                    ))}

                    {cta && <a href={ctaLink}>
                        <Button variant='outline' className="border-2">
                            {cta} {ctaIcon && <SVG src={ctaIcon.svg} className='size-5' style={{}} />}
                        </Button>
                    </a>}
                </CardContent>
            </Card>
        </div>
    )
}

export default Product