import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
	"group relative inline-flex items-center justify-center gap-0 md:gap-2 whitespace-nowrap rounded-full text-sm md:text-lg font-bold ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none transition-all duration-500 ease-in-out z-[1]",
	{
		variants: {
			variant: {
				default: "overflow-hidden border border-primary bg-transparent hover:scale-105 text-primary-foreground hover:text-foreground",
				destructive:
					"bg-destructive text-destructive-foreground hover:bg-destructive/90",
				outline:
					"overflow-hidden border border-primary bg-transparent hover:scale-105",
				secondary:
					"bg-secondary text-secondary-foreground hover:bg-secondary/80",
				ghost: "hover:bg-accent hover:text-accent-foreground",
				link: "text-primary underline-offset-4 hover:underline",
			},
			size: {
				default: "h-10 p-3 md:px-5 md:py-6 text-sm md:text-xl [&_svg]:stroke-[3] md:[&_svg]:stroke-[5]",
				sm: "h-10 px-4 py-2 text-sm",
				lg: "h-10 py-6 px-3 md:px-5 md:py-8 gap-1 text-base md:text-2xl [&_svg]:stroke-[4] md:[&_svg]:stroke-[6]",
				icon: "h-10 w-10",
			},
		},
		defaultVariants: {
			variant: "default",
			size: "default",
		},
	}
)

export interface ButtonProps
	extends React.ButtonHTMLAttributes<HTMLButtonElement>,
	VariantProps<typeof buttonVariants> {
	asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
	({ className, variant, size, asChild = false, ...props }, ref) => {
		const Comp = asChild ? Slot : "button"
		variant = variant || "default";
		return (
			<Comp
				data-variant={variant}
				className={cn(buttonVariants({ variant, size, className }))}
				ref={ref}
				{...props}
			>
				{props.children}
				{variant === "outline" && (<>
					<svg xmlns="http://www.w3.org/2000/svg" version="1.1" className="block h-0 w-0">
						<defs>
							<filter id="goo">
								<feGaussianBlur in="SourceGraphic" stdDeviation="10" result="blur"></feGaussianBlur>
								<feColorMatrix in="blur" mode="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 18 -7" result="goo"></feColorMatrix>
								<feBlend in="SourceGraphic" in2="goo"></feBlend>
							</filter>
						</defs>
					</svg>
					<div className="button__blobs *:bg-primary *:group-hover:brightness-[0.8] *:group-hover:scale-[1.4] *:group-hover:translate-y-0 *:group-hover:translate-x-0 *:-translate-y-[125%] *:scale-[1.4]">
						<div></div>
						<div></div>
						<div></div>
					</div>
				</>)}
				{variant === "default" && (<>
					<svg xmlns="http://www.w3.org/2000/svg" version="1.1" className="block h-0 w-0">
						<defs>
							<filter id="goo">
								<feGaussianBlur in="SourceGraphic" stdDeviation="10" result="blur"></feGaussianBlur>
								<feColorMatrix in="blur" mode="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 18 -7" result="goo"></feColorMatrix>
								<feBlend in="SourceGraphic" in2="goo"></feBlend>
							</filter>
						</defs>
					</svg>
					<div className="button__blobs *:bg-primary *:group-hover:brightness-[0.8] *:group-hover:scale-[1.4] *:group-hover:translate-y-[125%] *:scale-[1.4]">
						<div></div>
						<div></div>
						<div></div>
					</div>
				</>)}
			</Comp>
		)
	}
)
Button.displayName = "Button"

export { Button, buttonVariants }
