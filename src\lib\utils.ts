import { ContentHandlerProps } from "@/components/sections/sanityTypes";
import { clsx, type ClassValue } from "clsx";
import React from "react";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs));
}

export function scrollDown(e: React.SyntheticEvent) {
	e.preventDefault();
	window.scrollTo({
		top: window.innerHeight,
		behavior: "smooth",
	});
}

export function splitStringInRatio(input: string, ratio1 = 7, ratio2 = 3) {
	if (!input) return { part1: "", part2: "" };

	// Trim the input and split it into words
	const words = input.trim().split(/\s+/);
	if (words.length === 1) return { part1: words[0], part2: "" };
	else if (words.length === 2) return { part1: words[0], part2: words[1] };

	// Calculate total lengths for the ratios
	const totalLength = words.reduce((sum, word) => sum + word.length, 0);
	const part1Length = Math.floor((ratio1 / (ratio1 + ratio2)) * totalLength);

	let currentLength = 0;
	let splitIndex = 0;

	// Find the index where the split should occur
	for (let i = 0; i < words.length; i++) {
		currentLength += words[i].length + 1; // +1 accounts for spaces between words
		if (currentLength > part1Length) {
			splitIndex = i;
			break;
		}
	}

	// Combine the words into two parts
	const part1 = words.slice(0, splitIndex).join(" ");
	const part2 = words.slice(splitIndex).join(" ");

	return { part1, part2 };
}

export function isContentEmpty(content: ContentHandlerProps | undefined): boolean {
	if (!content) return true;

	switch (content.type) {
		case "Big List":
			return content.bigList!.length <= 0;
		case "Carousel":
			return content.carousel!.contentType == "iconCard" ? content.carousel!.iconCards.length <= 0 : content.carousel!.carousel.length <= 0;
		case "Columns":
			return isContentEmpty(content.columns!.contentLeft) && isContentEmpty(content.columns!.contentRight);
		case "Employees":
			return content.employees!.length <= 0;
		case "Form":
			return content.form!.formFields.length <= 0 && !content.form!.title;
		case "Icon Cards":
			return content.iconCards!.length <= 0;
		case "Icons":
			return content.icons!.length <= 0;
		case "Offices":
			return content.offices!.length <= 0;
		case "Tabs":
			return content.tabs!.length <= 0;
		case "Timeline":
			return content.timeline!.length <= 0;
		case "default":
			return content.content!.length <= 0;
		case "Product":
		default:
			return false;
	}
}
