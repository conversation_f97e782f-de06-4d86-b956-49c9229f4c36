import { SEOData } from '@/components/sections/sanityTypes';
import React, { useEffect } from 'react';

type SEOProps = {
    seo: SEOData
}

export const SEO: React.FC<SEOProps> = ({ seo }) => {
    const {
        title,
        description,
        keywords = [],
        canonicalUrl,
        ogTitle,
        ogDescription,
        ogImage,
        noindex,
        structuredData,
    } = seo;

    const ogImageUrl = ogImage?.asset.url;

    return (
        <>
            <title>{title} | MBS</title>
            <meta name="description" content={description} />
            {keywords.length > 0 && <meta name="keywords" content={keywords.join(", ")} />}
            {noindex && <meta name="robots" content="noindex, nofollow" />}

            {/* Canonical URL */}
            {canonicalUrl && <link rel="canonical" href={canonicalUrl} />}

            {/* Open Graph Meta Tags */}
            <meta property="og:title" content={ogTitle || title} />
            <meta property="og:description" content={ogDescription || description} />
            {ogImageUrl && <meta property="og:image" content={ogImageUrl} />}
            <meta property="og:type" content="website" />

            {/* Twitter Meta Tags (reusing Open Graph values for consistency) */}
            <meta name="twitter:card" content="summary_large_image" />
            <meta name="twitter:title" content={ogTitle || title} />
            <meta name="twitter:description" content={ogDescription || description} />
            {ogImageUrl && <meta name="twitter:image" content={ogImageUrl} />}

            {/* Structured Data (JSON-LD) */}
            {structuredData && (
                <script type="application/ld+json">
                    {structuredData}
                </script>
            )}
        </>
    )
}