import useEmblaCarousel, { type UseEmblaCarouselType } from "embla-carousel-react";
import { ArrowLeft, ArrowRight } from "lucide-react";
import * as React from "react";

import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { cva, VariantProps } from "class-variance-authority";

type CarouselApi = UseEmblaCarouselType[1];
type UseCarouselParameters = Parameters<typeof useEmblaCarousel>;
type CarouselOptions = UseCarouselParameters[0];
type CarouselPlugin = UseCarouselParameters[1];

type CarouselProps = {
	opts?: CarouselOptions;
	plugins?: CarouselPlugin;
	orientation?: "horizontal" | "vertical";
	setApi?: (api: CarouselApi) => void;
};

type CarouselContextProps = {
	carouselRef: ReturnType<typeof useEmblaCarousel>[0];
	api: ReturnType<typeof useEmblaCarousel>[1];
	scrollPrev: () => void;
	scrollNext: () => void;
	canScrollPrev: boolean;
	canScrollNext: boolean;
} & CarouselProps;

const CarouselContext = React.createContext<CarouselContextProps | null>(null);

function useCarousel() {
	const context = React.useContext(CarouselContext);

	if (!context) {
		throw new Error("useCarousel must be used within a <Carousel />");
	}

	return context;
}

const Carousel = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement> & CarouselProps>(({ orientation = "horizontal", opts, setApi, plugins, className, children, ...props }, ref) => {
	const [carouselRef, api] = useEmblaCarousel(
		{
			...opts,
			axis: orientation === "horizontal" ? "x" : "y",
		},
		plugins
	);
	const [canScrollPrev, setCanScrollPrev] = React.useState(false);
	const [canScrollNext, setCanScrollNext] = React.useState(false);

	const onSelect = React.useCallback((api: CarouselApi) => {
		if (!api) {
			return;
		}

		setCanScrollPrev(api.canScrollPrev());
		setCanScrollNext(api.canScrollNext());
	}, []);

	const scrollPrev = React.useCallback(() => {
		api?.scrollPrev();
	}, [api]);

	const scrollNext = React.useCallback(() => {
		api?.scrollNext();
	}, [api]);

	const handleKeyDown = React.useCallback(
		(event: React.KeyboardEvent<HTMLDivElement>) => {
			if (event.key === "ArrowLeft") {
				event.preventDefault();
				scrollPrev();
			} else if (event.key === "ArrowRight") {
				event.preventDefault();
				scrollNext();
			}
		},
		[scrollPrev, scrollNext]
	);

	React.useEffect(() => {
		if (!api || !setApi) {
			return;
		}

		setApi(api);
	}, [api, setApi]);

	React.useEffect(() => {
		if (!api) {
			return;
		}

		onSelect(api);
		api.on("reInit", onSelect);
		api.on("select", onSelect);

		return () => {
			api?.off("select", onSelect);
		};
	}, [api, onSelect]);

	return (
		<CarouselContext.Provider
			value={{
				carouselRef,
				api: api,
				opts,
				orientation: orientation || (opts?.axis === "y" ? "vertical" : "horizontal"),
				scrollPrev,
				scrollNext,
				canScrollPrev,
				canScrollNext,
			}}
		>
			<div ref={ref} onKeyDownCapture={handleKeyDown} className={cn("relative", className)} role="region" aria-roledescription="carousel" {...props}>
				{children}
			</div>
		</CarouselContext.Provider>
	);
});
Carousel.displayName = "Carousel";

const CarouselContent = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(({ className, ...props }, ref) => {
	const { carouselRef, orientation } = useCarousel();

	return (
		<div ref={carouselRef} className="overflow-hidden h-full">
			<div ref={ref} className={cn("flex", orientation === "horizontal" ? "-ml-4" : "flex-col", className)} {...props} />
		</div>
	);
});
CarouselContent.displayName = "CarouselContent";

const CarouselItem = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(({ className, ...props }, ref) => {
	const { orientation } = useCarousel();

	return <div ref={ref} role="group" aria-roledescription="slide" className={cn("min-w-0 shrink-0 grow-0 basis-full", orientation === "horizontal" ? "pl-4" : "", className)} {...props} />;
});
CarouselItem.displayName = "CarouselItem";

const CarouselPrevious = React.forwardRef<HTMLButtonElement, React.ComponentProps<typeof Button>>(({ className, variant = "outline", size = "icon", ...props }, ref) => {
	const { orientation, scrollPrev, canScrollPrev } = useCarousel();

	return (
		<Button
			ref={ref}
			variant={variant}
			size={size}
			className={cn(
				"absolute  h-8 w-8 rounded-full ml-5 md:ml-0 border-input",
				orientation === "horizontal" ? "-left-12 top-1/2 -translate-y-1/2" : "-top-12 left-1/2 -translate-x-1/2 rotate-90",
				className
			)}
			disabled={!canScrollPrev}
			onClick={scrollPrev}
			{...props}
		>
			<ArrowLeft className="h-4 w-4 absolute" />
			<span className="sr-only">Previous slide</span>
		</Button>
	);
});
CarouselPrevious.displayName = "CarouselPrevious";

const CarouselNext = React.forwardRef<HTMLButtonElement, React.ComponentProps<typeof Button>>(({ className, variant = "outline", size = "icon", ...props }, ref) => {
	const { orientation, scrollNext, canScrollNext } = useCarousel();

	return (
		<Button
			ref={ref}
			variant={variant}
			size={size}
			className={cn(
				"absolute h-8 w-8 rounded-full mr-5 md:mr-0 border-input",
				orientation === "horizontal" ? "-right-12 top-1/2 -translate-y-1/2" : "-bottom-12 left-1/2 -translate-x-1/2 rotate-90",
				className
			)}
			disabled={!canScrollNext}
			onClick={scrollNext}
			{...props}
		>
			<ArrowRight className="h-4 w-4 absolute" />
			<span className="sr-only">Next slide</span>
		</Button>
	);
});
CarouselNext.displayName = "CarouselNext";

const IndicatorVariants = cva("relative block rounded-full h-2 w-2 mx-1 transition-all duration-700 shadow-black ease-in-out", {
	variants: {
		variant: {
			primary: "bg-primary hover:bg-primary/90",
			secondary: "bg-secondary hover:bg-secondary/80",
			outline: "bg-transparent hover:bg-accent",
		},
		extended: {
			true: "w-8",
			false: "aspect-square",
		},
	},
	defaultVariants: {
		variant: "primary",
		extended: false,
	},
});

interface IndicatorProps extends React.HTMLAttributes<HTMLDivElement>, VariantProps<typeof IndicatorVariants> {
	hide?: {
		"2xl": number;
		lg: number;
		md: number;
		default: number;
	};
}

const CarouselIndicator = React.forwardRef<HTMLDivElement, IndicatorProps>(({ className, variant, hide, ...props }, ref) => {
	const { api } = useCarousel();

	const [currentIndex, setCurrentIndex] = React.useState(0);

	hide = {
		"2xl": hide?.["2xl"] || 0,
		lg: hide?.lg || 0,
		md: hide?.md || 0,
		default: hide?.default || 0,
	};

	const hiddenClass = (index: number, length: number) => {
		const classes = {
			"2xl": index < length - hide?.["2xl"] ? "" : "2xl:hidden",
			lg: index < length - hide?.lg ? "" : "lg:hidden",
			md: index < length - hide?.md ? "" : "md:hidden",
			default: index < length - hide?.default ? "" : "hidden",
		};
		return `${classes["2xl"]} ${classes["lg"]} ${classes["md"]} ${classes["default"]}`;
	};

	const onSelect = React.useCallback((emblaApi: typeof api) => {
		setCurrentIndex(emblaApi?.selectedScrollSnap() || 0);
	}, []);

	const scrollTo = (e: React.SyntheticEvent, index: number) => {
		e.preventDefault();
		api?.scrollTo(index);
	};

	React.useEffect(() => {
		if (!api) return;

		onSelect(api);
		api.on("reInit", onSelect).on("select", onSelect);
	}, [api, onSelect]);

	return (
		<div
			className={cn(
				"flex",
				// orientation == "horizontal" ?
				// "flex-row" : "flex-col",
				className
			)}
			ref={ref}
			{...props}
		>
			{api?.slideNodes().map((_, index) => (
				<button onClick={(e) => scrollTo(e, index)} key={index} className={cn(hiddenClass(index, api?.slideNodes().length), IndicatorVariants({ variant, extended: currentIndex == index }))}>
					{currentIndex == index && <div className="top-0 absolute h-full w-full bg-foreground/30 rounded-full animate-pulse duration-[3s]" />}
				</button>
			))}
		</div>
	);
});
CarouselIndicator.displayName = "CarouselIndicator";

export { Carousel, CarouselContent, CarouselIndicator, CarouselItem, CarouselNext, CarouselPrevious, type CarouselApi };
