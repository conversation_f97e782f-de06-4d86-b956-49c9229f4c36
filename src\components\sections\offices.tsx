import { Office } from "@/components/sections/sanityTypes";
import { useSection } from "@/components/sections/section";
import { Card } from "@/components/ui/card";
import { Typography } from "@/components/ui/typography";
import { Mail, MapPin, Phone, Printer } from "lucide-react";
import React, { useEffect, useState } from "react";

const Offices: React.FC<{ offices: Office[] }> = ({ offices }) => {
	const { hasTitle } = useSection();

	const [isClient, setIsClient] = useState(false);

	useEffect(() => {
		setIsClient(true); // Ensures that the map is only rendered client-side
		updateLeafletOptions();
	}, []);

	const { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Popup, TileLayer, useMap } = isClient
		? require("react-leaflet")
		: {
				MapContainer: <div />,
				Marker: <div />,
				Popup: <div />,
				TileLayer: <div />,
				useMap: () => null,
		  };

	const PanToLocation = ({ coords }: { coords: [number, number] }) => {
		const map = useMap();
		map?.flyTo(coords, 15, { duration: 2 });
		return null;
	};

	function updateLeafletOptions() {
		const L = require("leaflet");

		L.Marker.prototype.options.icon = L.icon({
			iconUrl: "https://cdn.sanity.io/images/rgew5gg8/production/60a90bcbb2b42b7ddb4556db94eb7c1084b0e5da-25x41.png",
			shadowUrl: "https://cdn.sanity.io/images/rgew5gg8/production/7b6a8df63930381e96604e705168d0527d6b82bc-41x41.png",
			iconSize: [25, 41],
			iconAnchor: [12, 41],
			popupAnchor: [1, -34],
		});
	}

	const [activeLocation, setActiveLocation] = useState(offices[0]);

	const handleTabClick = (office: Office) => {
		setActiveLocation(office);
	};

	function openPopup(e: L.LeafletEvent) {
		e.target.openPopup();
	}

	function getCoords(office: Office): [number, number] {
		return [office.location.lat, office.location.lng];
	}

	return (
		<div className="mt-10 grid grid-cols-2 lg:grid-cols-4 gap-x-4 gap-y-4 text-muted rounded-lg lg:p-8">
			{/* Map Section */}
			{isClient && (
				<div className="col-span-2 lg:col-span-3 min-h-[50vh] shadow-lg">
					<MapContainer center={getCoords(activeLocation)} zoom={15} className="h-full w-full rounded-lg">
						<TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" attribution='&copy; <a href="http://osm.org/copyright">OpenStreetMap</a> contributors' />
						<PanToLocation coords={getCoords(activeLocation)} />
						{offices.map((location, index) => (
							<Marker key={index} position={getCoords(location)} eventHandlers={{ add: openPopup }}>
								<Popup autoClose={false} autoPan={false}>
									<Typography className="w-64">{location.address}</Typography>
								</Popup>
							</Marker>
						))}
					</MapContainer>
				</div>
			)}

			{/* Tabs Section */}
			<Card className="h-full p-4 col-span-2 lg:col-span-1 shadow-lg flex flex-col">
				<Typography component={hasTitle ? "h3" : "h2"} size="xxl" className="hidden: lg:block mb-5">
					Our Locations
				</Typography>
				<div className="flex flex-row flex-wrap lg:flex-col gap-2 flex-1 bg-muted text-muted-foreground rounded-md p-2 justify-center lg:justify-start">
					{offices.map((location, index) => (
						<div
							key={index}
							onClick={() => handleTabClick(location)}
							className={`cursor-pointer ${
								activeLocation.name === location.name ? "bg-background text-foreground shadow-sm before:scale-y-100" : "before:scale-y-0"
							} inline-flex flex-col justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm md:text-lg font-medium before:origin-bottom before:transition-all before:duration-200 relative before:h-full before:left-0  before:rounded-l-sm before:w-1 before:bg-primary before:absolute w-full text-wrap`}
						>
							{location.name}
							<span className="hidden lg:block font-light">{location.description}</span>
						</div>
					))}
				</div>
			</Card>

			{/* Description */}
			<Card className="col-span-2 lg:col-span-4 p-4 shadow-lg">
				<Typography size="xl" component="h4">
					{activeLocation.name}
					{activeLocation.description && <span className="text-primary"> - {activeLocation.description}</span>}
				</Typography>

				<Typography className="mt-2 flex gap-2">
					<MapPin className="size-5 my-auto shrink-0" />
					{activeLocation.address}
				</Typography>

				<Typography className="mt-2 flex gap-2">
					<Phone className="size-5 my-auto shrink-0" />
					{activeLocation.phones.map((phone) => `${phone}${activeLocation.phones.indexOf(phone) === activeLocation.phones.length - 1 ? "" : " / "}`)}
				</Typography>

				{activeLocation.fax && (
					<Typography className="mt-2 flex gap-2">
						<Printer className="size-5 my-auto shrink-0" />
						{activeLocation.fax}
					</Typography>
				)}
				<Typography className="mt-2">
					<a href={`mailto:${activeLocation.email}`} className="flex gap-2 break-all text-secondary w-fit">
						<Mail className="size-5 my-auto shrink-0" /> {activeLocation.email}
					</a>
				</Typography>
			</Card>
		</div>
	);
};

export default Offices;
