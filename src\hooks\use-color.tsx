import { graphql, useStaticQuery } from "gatsby";

type rawHSL = {
    a: number;
    h: number;
    l: number;
    s: number;
}

type rawTheme = {
    background: { _rawHsl: rawHSL };
    foreground: { _rawHsl: rawHSL };
    foregroundText: { _rawHsl: rawHSL };
    card: { _rawHsl: rawHSL };
    cardForeground: { _rawHsl: rawHSL };
    popover: { _rawHsl: rawHSL };
    popoverForeground: { _rawHsl: rawHSL };
    primary: { _rawHsl: rawHSL };
    primaryForeground: { _rawHsl: rawHSL };
    secondary: { _rawHsl: rawHSL };
    secondaryForeground: { _rawHsl: rawHSL };
    muted: { _rawHsl: rawHSL };
    mutedForeground: { _rawHsl: rawHSL };
    accent: { _rawHsl: rawHSL };
    accentForeground: { _rawHsl: rawHSL };
    destructive: { _rawHsl: rawHSL };
    destructiveForeground: { _rawHsl: rawHSL };
    border: { _rawHsl: rawHSL };
    input: { _rawHsl: rawHSL };
    ring: { _rawHsl: rawHSL };
    radius: string;
    chart1: { _rawHsl: rawHSL };
    chart2: { _rawHsl: rawHSL };
    chart3: { _rawHsl: rawHSL };
    chart4: { _rawHsl: rawHSL };
    chart5: { _rawHsl: rawHSL };
    sidebarBackground: { _rawHsl: rawHSL };
    sidebarForeground: { _rawHsl: rawHSL };
    sidebarPrimary: { _rawHsl: rawHSL };
    sidebarPrimaryForeground: { _rawHsl: rawHSL };
    sidebarAccent: { _rawHsl: rawHSL };
    sidebarAccentForeground: { _rawHsl: rawHSL };
    sidebarBorder: { _rawHsl: rawHSL };
    sidebarRing: { _rawHsl: rawHSL };
}

type themeQueryResult = {
    sanityTheme: rawTheme;
}

type theme = {
    background: string;
    foreground: string;
    foregroundText: string;
    card: string;
    cardForeground: string;
    popover: string;
    popoverForeground: string;
    primary: string;
    primaryForeground: string;
    secondary: string;
    secondaryForeground: string;
    muted: string;
    mutedForeground: string;
    accent: string;
    accentForeground: string;
    destructive: string;
    destructiveForeground: string;
    border: string;
    input: string;
    ring: string;
    radius: string;
    chart1: string;
    chart2: string;
    chart3: string;
    chart4: string;
    chart5: string;
    sidebarBackground: string;
    sidebarForeground: string;
    sidebarPrimary: string;
    sidebarPrimaryForeground: string;
    sidebarAccent: string;
    sidebarAccentForeground: string;
    sidebarBorder: string;
    sidebarRing: string;
}

function rawHSLToString(hsl: rawHSL): string {
    return `${hsl.h} ${hsl.s * 100}% ${hsl.l * 100}%`;
}

const useColorTheme = () => {
    const query: themeQueryResult = useStaticQuery(graphql`
        query {
            sanityTheme {
                background { _rawHsl }
                foreground { _rawHsl }
                foregroundText { _rawHsl }
                card { _rawHsl }
                cardForeground { _rawHsl }
                popover { _rawHsl }
                popoverForeground { _rawHsl }
                primary { _rawHsl }
                primaryForeground { _rawHsl }
                secondary { _rawHsl }
                secondaryForeground { _rawHsl }
                muted { _rawHsl }
                mutedForeground { _rawHsl }
                accent { _rawHsl }
                accentForeground { _rawHsl }
                destructive { _rawHsl }
                destructiveForeground { _rawHsl }
                border { _rawHsl }
                input { _rawHsl }
                ring { _rawHsl }
                radius
                chart1 { _rawHsl }
                chart2 { _rawHsl }
                chart3 { _rawHsl }
                chart4 { _rawHsl }
                chart5 { _rawHsl }
                sidebarBackground { _rawHsl }
                sidebarForeground { _rawHsl }
                sidebarPrimary { _rawHsl }
                sidebarPrimaryForeground { _rawHsl }
                sidebarAccent { _rawHsl }
                sidebarAccentForeground { _rawHsl }
                sidebarBorder { _rawHsl }
                sidebarRing { _rawHsl }
            }
        }
    `);

    const theme: theme = {
        background: rawHSLToString(query.sanityTheme.background._rawHsl),
        foreground: rawHSLToString(query.sanityTheme.foreground._rawHsl),
        foregroundText: rawHSLToString(query.sanityTheme.foregroundText._rawHsl),
        card: rawHSLToString(query.sanityTheme.card._rawHsl),
        cardForeground: rawHSLToString(query.sanityTheme.cardForeground._rawHsl),
        popover: rawHSLToString(query.sanityTheme.popover._rawHsl),
        popoverForeground: rawHSLToString(query.sanityTheme.popoverForeground._rawHsl),
        primary: rawHSLToString(query.sanityTheme.primary._rawHsl),
        primaryForeground: rawHSLToString(query.sanityTheme.primaryForeground._rawHsl),
        secondary: rawHSLToString(query.sanityTheme.secondary._rawHsl),
        secondaryForeground: rawHSLToString(query.sanityTheme.secondaryForeground._rawHsl),
        muted: rawHSLToString(query.sanityTheme.muted._rawHsl),
        mutedForeground: rawHSLToString(query.sanityTheme.mutedForeground._rawHsl),
        accent: rawHSLToString(query.sanityTheme.accent._rawHsl),
        accentForeground: rawHSLToString(query.sanityTheme.accentForeground._rawHsl),
        destructive: rawHSLToString(query.sanityTheme.destructive._rawHsl),
        destructiveForeground: rawHSLToString(query.sanityTheme.destructiveForeground._rawHsl),
        border: rawHSLToString(query.sanityTheme.border._rawHsl),
        input: rawHSLToString(query.sanityTheme.input._rawHsl),
        ring: rawHSLToString(query.sanityTheme.ring._rawHsl),
        radius: query.sanityTheme.radius,
        chart1: rawHSLToString(query.sanityTheme.chart1._rawHsl),
        chart2: rawHSLToString(query.sanityTheme.chart2._rawHsl),
        chart3: rawHSLToString(query.sanityTheme.chart3._rawHsl),
        chart4: rawHSLToString(query.sanityTheme.chart4._rawHsl),
        chart5: rawHSLToString(query.sanityTheme.chart5._rawHsl),
        sidebarBackground: rawHSLToString(query.sanityTheme.sidebarBackground._rawHsl),
        sidebarForeground: rawHSLToString(query.sanityTheme.sidebarForeground._rawHsl),
        sidebarPrimary: rawHSLToString(query.sanityTheme.sidebarPrimary._rawHsl),
        sidebarPrimaryForeground: rawHSLToString(query.sanityTheme.sidebarPrimaryForeground._rawHsl),
        sidebarAccent: rawHSLToString(query.sanityTheme.sidebarAccent._rawHsl),
        sidebarAccentForeground: rawHSLToString(query.sanityTheme.sidebarAccentForeground._rawHsl),
        sidebarBorder: rawHSLToString(query.sanityTheme.sidebarBorder._rawHsl),
        sidebarRing: rawHSLToString(query.sanityTheme.sidebarRing._rawHsl),
    }

    const rootVars = `
        :root {
            --background: ${theme.background || "0 0% 100%"};
            --foreground: ${theme.foreground || "217.2 32.6% 17.5%"};
            --foreground-text: ${theme.foregroundText || "217 5% 65%"};
            --card: ${theme.card || "0 0% 100%"};
            --card-foreground: ${theme.cardForeground || "217.2 32.6% 17.5%"};
            --popover: ${theme.popover || "0 0% 100%"};
            --popover-foreground: ${theme.popoverForeground || "217.2 32.6% 17.5%"};
            --primary: ${theme.primary || "359 74% 51%"};
            --primary-foreground: ${theme.primaryForeground || "0 85.7% 97.3%"};
            --secondary: ${theme.secondary || "207 75% 53%"};
            --secondary-foreground: ${theme.secondaryForeground || "0 0% 100%"};
            --muted: ${theme.muted || "0 0% 96.1%"};
            --muted-foreground: ${theme.mutedForeground || "215.4 16.3% 46.9%"};
            --accent: ${theme.accent || "0 0% 96.1%"};
            --accent-foreground: ${theme.accentForeground || "0 0% 9%"};
            --destructive: ${theme.destructive || "0 84.2% 60.2%"};
            --destructive-foreground: ${theme.destructiveForeground || "0 0% 98%"};
            --border: ${theme.border || "0 0% 89.8%"};
            --input: ${theme.input || "0 0% 89.8%"};
            --ring: ${theme.ring || "359 74% 51%"};
            --radius: ${theme.radius || "0.5rem"};
            --chart-1: ${theme.chart1 || "12 76% 61%"};
            --chart-2: ${theme.chart2 || "173 58% 39%"};
            --chart-3: ${theme.chart3 || "197 37% 24%"};
            --chart-4: ${theme.chart4 || "43 74% 66%"};
            --chart-5: ${theme.chart5 || "27 87% 67%"};
            --sidebar-background: ${theme.sidebarBackground || "0 0% 98%"};
            --sidebar-foreground: ${theme.sidebarForeground || "240 5.3% 26.1%"};
            --sidebar-primary: ${theme.sidebarPrimary || "240 5.9% 10%"};
            --sidebar-primary-foreground: ${theme.sidebarPrimaryForeground || "0 0% 98%"};
            --sidebar-accent: ${theme.sidebarAccent || "240 4.8% 95.9%"};
            --sidebar-accent-foreground: ${theme.sidebarAccentForeground || "240 5.9% 10%"};
            --sidebar-border: ${theme.sidebarBorder || "220 13% 91%"};
            --sidebar-ring: ${theme.sidebarRing || "217.2 91.2% 59.8%"};
        }
    `;

    // dark selector
    const darkVars = `
        .dark {
            --background: ${theme.foreground || "217.2 32.6% 17.5%"};
            --foreground: ${theme.muted || "0 0% 96.1%"};
            --card: ${theme.cardForeground || "217.2 32.6% 17.5%"};
            --card-foreground: ${theme.muted || "0 0% 96.1%"};
            --popover: 0 0% 3.9%;
            --popover-foreground: ${theme.muted || "0 0% 96.1%"};
            --primary: ${theme.primary || "359 74% 51%"};
            --primary-foreground: ${theme.primaryForeground || "0 85.7% 97.3%"};
            --secondary: ${theme.secondary || "207 75% 53%"};
            --secondary-foreground: ${theme.secondaryForeground || "0 0% 100%"};
            --muted: 0 0% 14.9%;
            --muted-foreground: ${theme.foregroundText || "217 5% 65%"};
            --accent: 0 0% 14.9%;
            --accent-foreground: ${theme.muted || "0 0% 96.1%"};
            --destructive: 0 62.8% 30.6%;
            --destructive-foreground: ${theme.muted || "0 0% 96.1%"};
            --border: 0 0% 14.9%;
            --input: 0 0% 14.9%;
            --ring: 0 0% 83.1%;
            --chart-1: 220 70% 50%;
            --chart-2: 160 60% 45%;
            --chart-3: 30 80% 55%;
            --chart-4: 280 65% 60%;
            --chart-5: 340 75% 55%;
            --sidebar-background: 240 5.9% 10%;
            --sidebar-foreground: 240 4.8% 95.9%;
            --sidebar-primary: 224.3 76.3% 48%;
            --sidebar-primary-foreground: 0 0% 100%;
            --sidebar-accent: 240 3.7% 15.9%;
            --sidebar-accent-foreground: 240 4.8% 95.9%;
            --sidebar-border: 240 3.7% 15.9%;
            --sidebar-ring: 217.2 91.2% 59.8%;
        }
    `;

    return { rootVars, darkVars };
};

export default useColorTheme;