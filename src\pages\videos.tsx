import { SEO } from "@/components/layout/seo";
import HeroSec<PERSON> from "@/components/sections/hero";
import { pageQueryResult } from "@/components/sections/sanityTypes";
import Section from "@/components/sections/section";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardFooter, CardHeader } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Typography } from "@/components/ui/typography";
import { GetYoutubeVideos } from "@/lib/api";
import { graphql, HeadFC, PageProps } from "gatsby";
import { Loader2 } from "lucide-react";
import React, { useEffect, useState } from "react";

type VideoCardProps = {
    title: string;
    videoID: string;
    date: string;
}

const VideoCard: React.FC<VideoCardProps> = ({ videoID, title, date }) => {
    const dateString = new Date(date).toLocaleString('en-IN', {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
    })

    return (
        <Card className="hover:scale-105 ease-in-out duration-300 transition-all flex flex-col">
            <CardHeader>
                <iframe width="100%" height="100%" src={`https://www.youtube.com/embed/${videoID}`} title="YouTube video player" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerPolicy="strict-origin-when-cross-origin" allowFullScreen></iframe>
                <Typography component='h2' size='lg' weight='medium'>
                    {title}
                </Typography>
            </CardHeader>
            <CardFooter className="text-muted-foreground mt-auto">
                {dateString}
            </CardFooter>
        </Card>
    )
}

const Page: React.FC<PageProps<pageQueryResult>> = ({ data }) => {
    const hero = data.allSanityPage.nodes[0].hero;

    const [loading, setLoading] = useState(true);
    const [fetching, setFetching] = useState(false);

    const [videos, setVideos] = useState<VideoCardProps[]>([]);
    const [nextPageToken, setNextPageToken] = useState<string | boolean>(true);

    const getVideos = async () => {
        const data = await GetYoutubeVideos();
        setVideos(data.videos)
        setNextPageToken(data.nextPageToken);

        setLoading(false);
    }

    const fetchMore = async () => {
        if (!nextPageToken || typeof nextPageToken != 'string') return;
        setFetching(true);

        const data = await GetYoutubeVideos(nextPageToken)
        setVideos((prev) => [...prev, ...data.videos])
        setNextPageToken(data.nextPageToken);

        setFetching(false);
    }

    useEffect(() => {
        getVideos();
    }, []);

    return (
        <div className="w-full">
            <HeroSection {...hero} />

            <Section title="" subtitle="" id="videos" bg="default" statistics={[]} last>
                {loading ?
                    <div className="flex flex-col w-full justify-center items-center gap-4">
                        <Typography size='lg' weight='medium'>
                            Loading Videos
                        </Typography>
                        <Loader2 className="animate-spin text-primary" />
                    </div> :
                    <div className="px-10 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {videos.map((video, index) => <VideoCard key={index} {...video} />)}

                        <Button disabled={!nextPageToken || fetching} onClick={fetchMore} className="mx-auto col-span-1 md:col-span-2 lg:col-span-3 mt-10">
                            {fetching && <Loader2 className="animate-spin" />}
                            Load more
                        </Button>
                    </div>
                }
            </Section>
        </div>
    )
}

export const Head: HeadFC<pageQueryResult> = ({ data }) => <SEO seo={data.allSanityPage.nodes[0].seo} />

export default Page;

export const query = graphql`
  query VideosQuery {
    allSanityPage(filter: {slug: {current: {eq: "videos"}}}) {
      nodes {
        seo {
          title
          description
          keywords
          canonicalUrl
          ogTitle
          ogDescription
          ogImage {
            asset {
              publicUrl
              url
            }
          }
          noindex
          structuredData
        }
        hero {
          title
          subtitle
          cta
          ctaIcon {
            svg
          }
          ctaLink
          bgImage {
            asset {
              gatsbyImageData
              url
              publicUrl
            }
            alt
            title
          }
          imageAlign
        }
      }
    }
  }
`