/** @type {import('tailwindcss').Config} */
export const darkMode = ["class"];
export const content = [
    `./src/pages/**/*.{js,jsx,ts,tsx}`,
    `./src/components/**/*.{js,jsx,ts,tsx}`,
];
export const theme = {
    extend: {
        maxWidth: {
            'screen': '100vw'
        },
        maxHeight: {
            'screen': '100vh'
        },
        flex: {
            'break': '1 1 100%'
        },
        dropShadow: {
            'outline': '0 2px 2px rgba(30, 41, 59, 0.8)'
        },
        boxShadow: {
            'center-sm': '0 0 2px 0 rgba(0,0,0,0.1)',
            'center': '0 0 4px 0 rgba(0,0,0,0.1)',
            'center-md': '0 0 6px 0 rgba(0,0,0,0.1)',
            'center-lg': '0 0 8px 0 rgba(0,0,0,0.1)',
            'center-xl': '0 0 12px 0 rgba(0,0,0,0.1)',
            'center-2xl': '0 0 16px 0 rgba(0,0,0,0.1)',
            'center-3xl': '0 0 24px 0 rgba(0,0,0,0.1)',
        },
        borderRadius: {
            lg: 'var(--radius)',
            md: 'calc(var(--radius) - 2px)',
            sm: 'calc(var(--radius) - 4px)'
        },
        colors: {
            background: 'hsl(var(--background))',
            foreground: {
                DEFAULT: 'hsl(var(--foreground))',
                text: 'hsl(var(--foreground-text))',
            },
            card: {
                DEFAULT: 'hsl(var(--card))',
                foreground: 'hsl(var(--card-foreground))'
            },
            popover: {
                DEFAULT: 'hsl(var(--popover))',
                foreground: 'hsl(var(--popover-foreground))'
            },
            primary: {
                DEFAULT: 'hsl(var(--primary))',
                foreground: 'hsl(var(--primary-foreground))'
            },
            secondary: {
                DEFAULT: 'hsl(var(--secondary))',
                foreground: 'hsl(var(--secondary-foreground))'
            },
            muted: {
                DEFAULT: 'hsl(var(--muted))',
                foreground: 'hsl(var(--muted-foreground))'
            },
            accent: {
                DEFAULT: 'hsl(var(--accent))',
                foreground: 'hsl(var(--accent-foreground))'
            },
            destructive: {
                DEFAULT: 'hsl(var(--destructive))',
                foreground: 'hsl(var(--destructive-foreground))'
            },
            border: 'hsl(var(--border))',
            input: 'hsl(var(--input))',
            ring: 'hsl(var(--ring))',
            chart: {
                '1': 'hsl(var(--chart-1))',
                '2': 'hsl(var(--chart-2))',
                '3': 'hsl(var(--chart-3))',
                '4': 'hsl(var(--chart-4))',
                '5': 'hsl(var(--chart-5))'
            },
            sidebar: {
                DEFAULT: 'hsl(var(--sidebar-background))',
                foreground: 'hsl(var(--sidebar-foreground))',
                primary: 'hsl(var(--sidebar-primary))',
                'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
                accent: 'hsl(var(--sidebar-accent))',
                'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
                border: 'hsl(var(--sidebar-border))',
                ring: 'hsl(var(--sidebar-ring))'
            }
        },
        keyframes: {
            'accordion-down': {
                from: {
                    height: '0'
                },
                to: {
                    height: 'var(--radix-accordion-content-height)'
                }
            },
            'accordion-up': {
                from: {
                    height: 'var(--radix-accordion-content-height)'
                },
                to: {
                    height: '0'
                }
            },
            'scroll-bounce': {
                '0%, 20%, 50%, 80%, 100%': { transform: 'translateY(0)' },
                '40%': { transform: 'translateY(-20px)' },
                '60%': { transform: 'translateY(-10px)' }
            }
        },
        animation: {
            'accordion-down': 'accordion-down 0.2s ease-out',
            'accordion-up': 'accordion-up 0.2s ease-out',
            'scroll-bounce': 'scroll-bounce 3s infinite'
        }
    }
};
export const plugins = [
	require("tailwindcss-animate"),
];
