import { readFileSync } from "fs";
import { GatsbySSR } from "gatsby";
import { join } from "path";
import React from "react";

export const onRenderBody: GatsbySSR["onRenderBody"] = ({ setHeadComponents, setHtmlAttributes }) => {
	// fetch theme data from cache
	const rootCache = join(process.cwd(), ".cache", "theme-root.txt");
	const rootTheme = readFileSync(rootCache, "utf-8");

	const darkCache = join(process.cwd(), ".cache", "theme-dark.txt");
	const darkTheme = readFileSync(darkCache, "utf-8");

	const themeData = {
		rootVars: rootTheme,
		darkVars: darkTheme,
	};

	const head = [
		<style key="root-vars" id="root-vars">
			{themeData.rootVars}
		</style>,
		<style key="dark-vars" id="dark-vars">
			{themeData.darkVars}
		</style>,
		<link key="leaflet" rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossOrigin="" />,
		<meta key="google-site-verification" name="google-site-verification" content="vRlrZzIKq3PODu0FdI7tc_g4N3RhI3AO1uK0Q5aWVvk" />,

		// Security Headers:
		<meta
			httpEquiv="Content-Security-Policy"
			content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.google-analytics.com https://www.googletagmanager.com https://unpkg.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://unpkg.com; img-src 'self' data: https://cdn.sanity.io https://*.tile.openstreetmap.org https://api.mapbox.com; font-src 'self' https://fonts.gstatic.com; connect-src 'self' https://www.google-analytics.com https://www.googleapis.com; frame-src 'self' https://www.youtube.com;"
		/>,
		<meta httpEquiv="X-Frame-Options" content="SAMEORIGIN" />,
		<meta httpEquiv="Permissions-Policy" content="geolocation=(), microphone=(), camera=(), usb=(), payment=()" />,
		<meta httpEquiv="Referrer-Policy" content="strict-origin-when-cross-origin" />,
	];

	setHeadComponents(head);
	setHtmlAttributes({
		lang: "en",
	});
};
