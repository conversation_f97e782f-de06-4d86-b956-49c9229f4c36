import { TimelineItemProps } from "@/components/sections/sanityTypes";
import { useSection } from "@/components/sections/section";
import ScrollAnim from "@/components/ui/scrollAnim";
import { Typography } from "@/components/ui/typography";
import sanitySerializers from "@/lib/sanity";
import { PortableText } from "@portabletext/react";
import React from "react";

interface TimelinePropsWithAlign extends TimelineItemProps {
	onLeft: boolean;
}

const TimelineItem: React.FC<TimelinePropsWithAlign> = ({
	title,
	date,
	description,
	onLeft,
}) => {
	const { hasTitle, bg } = useSection();

	const dateString = new Date(date).getFullYear(); //.toLocaleDateString('en-in', { day: 'numeric', month: 'long', year: 'numeric' });

	return (
		<li
			className={`mb-8 flex justify-between items-center w-full ${
				onLeft ? "flex-row-reverse" : "flex-row"
			}`}
		>
			<div className="order-1 w-5/12" />
			<div className="order-2 w-1/6 pt-14 z-[1] mb-auto">
				<div
					className={`size-4 rounded-full ${
						bg === "dark"
							? "bg-accent/80 border-accent"
							: "bg-secondary/80 border-secondary"
					} border-2 mx-auto`}
				/>
			</div>
			<div
				className={`order-3 w-5/12 px-1 py-4 overflow-clip ${
					onLeft ? "text-right" : "text-left"
				} border-b ${
					bg === "dark" ? "border-border" : "border-foreground-text"
				}`}
			>
				<ScrollAnim
					direction={onLeft ? "right" : "left"}
					duration={0.7}
				>
					<Typography className="text-secondary mb-3">
						{dateString}
					</Typography>
				</ScrollAnim>
				<ScrollAnim
					direction={onLeft ? "right" : "left"}
					duration={0.7}
					delay={100}
				>
					<Typography
						size="lg"
						weight="bold"
						component={hasTitle ? "h3" : "h2"}
						className="mb-3"
					>
						{title}
					</Typography>
				</ScrollAnim>
				<ScrollAnim
					direction={onLeft ? "right" : "left"}
					duration={0.7}
					delay={200}
				>
					{description && (
						<PortableText
							value={description}
							components={sanitySerializers(
								`mb-2 last:mb-0 text-sm sm:text-base`
							)}
						/>
					)}
				</ScrollAnim>
			</div>
		</li>
	);
};

const Timeline: React.FC<{ timeline: TimelineItemProps[] }> = ({
	timeline,
}) => {
	return (
		<ul className="relative wrap overflow-hidden px-5 md:px-10 h-full">
			<div className="border-2 border-primary absolute h-full rounded-l-full right-1/2" />
			<div className="border-2 border-primary absolute h-full rounded-r-full left-1/2" />

			{timeline.map((item, i) => (
				<TimelineItem key={i} onLeft={i % 2 == 0} {...item} />
			))}
		</ul>
	);
};

export default Timeline;
