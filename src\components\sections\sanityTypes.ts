import { PortableTextBlock } from "@portabletext/react"
import { IGatsbyImageData } from "gatsby-plugin-image"

/**
 * All typings for data from sanity are defined here
 * Update this file when changing the schema in sanity
 */

/**
 * aligns the image
 */
export type imageAlign = 'left' | 'right' | 'center' | 'top' | 'bottom' | 'right top' | 'right bottom' | 'left top' | 'left bottom';

/**
 * The image type from sanity
 */
export type SanityImage = {
	/**
	 * The image data for gatsby
	 */
	asset: {
		/**
		 * The gatsby image data
		 */
		gatsbyImageData: IGatsbyImageData,
		/**
		 * The url of the image
		 */
		url: string,
		/**
		 * The public url of the image
		 */
		publicUrl: string
	},
	/**
	 * The alt text for the image
	 */
	alt: string,
	/**
	 * The title of the image
	 */
	title: string
}

/**
 * The result of the query for all pages
 */
export type pageQueryResult = {
	allSanityPage: {
		nodes: CMSPageProps[]
	}
}

/**
 * Props for page components
 */
export type CMSPageProps = {
	title: string,
	seo: SEOData,
	hero: HeroSectionProps,
	sections: SectionProps[]
}

/**
 * The SEO data for the page
 */
export type SEOData = {
	/**
	 * The meta title of the page
	 */
	title: string;

	/**
	 * The meta description of the page
	 */
	description: string;

	/**
	 * The keywords for the page
	 */
	keywords?: string[];

	/**
	 * The canonical url of the page
	 */
	canonicalUrl?: string;

	/**
	 * The open graph data (for social media)
	 */
	ogTitle?: string;
	ogDescription?: string;
	ogImage?: {
		asset: {
			url: string;
			publicUrl: string;
		};
	};

	/**
	 * Whether the page should be indexed by search engines
	 */
	noindex?: boolean;

	/**
	 * The structured data for the page (JSON-LD)
	 */
	structuredData?: string;
};

/**
 * The props for the hero section
 */
export type HeroSectionProps = {
	/**
	 * The title of the hero section
	 */
	title: string,

	/**
	 * Subtitle for hero section
	 */
	subtitle: string,

	/**
	 * The call to action text
	 */
	cta?: string,

	/**
	 * The call to action icon
	 */
	ctaIcon?: { svg: string },

	/**
	 * The call to action link
	 */
	ctaLink?: string,

	/**
	 * The background image for the hero section
	 */
	bgImage?: SanityImage,

	/**
	 * The alignment of the background image
	 */
	imageAlign: imageAlign
}

export type SectionProps = {
	title: string,
	subtitle: string,
	id: string,
	bg?: 'default' | 'dark',
	content?: ContentHandlerProps,
	statistics: StatisticProps[]
}

export type StatisticProps = {
	type: 'image' | 'icon' | 'none'
	image?: SanityImage,
	icon?: { svg: string },
	titleNumber?: number,
	titleString: string,
	subtitle: string
}

export type ContentHandlerProps = {
	type: 'default' | 'Carousel' | 'Icons' | 'Tabs' | 'Product' | 'Big List' | 'Icon Cards' | 'Columns' | 'Employees' | 'Offices' | 'Timeline' | 'Form',
	content?: DefaultContentProps[],
	carousel?: CarouselProps,
	icons?: IconProps[],
	tabs?: TabItemProps[],
	product?: ProductProps,
	bigList?: ListItemProps[],
	iconCards?: IconCardProps[],
	columns?: ColumnProps,
	employees?: Employee[],
	offices?: Office[],
	timeline?: TimelineItemProps[],
	form?: DynamicFormProps,
}

export type DefaultContentProps = {
	title: string,
	content: PortableTextBlock[],
	cta: string,
	ctaIcon: { svg: string },
	ctaLink: string,
	image: SanityImage,
	imageAlign: imageAlign,
	imageAsBg?: boolean
}

export type CarouselProps = {
	type: 'Full' | 'Half' | 'Third' | 'Quarter' | 'Mini',
	delay: number,
	contentType: 'defaultContent' | 'iconCard',
	carousel: DefaultContentProps[],
	iconCards: IconCardProps[],
	autoscroll: boolean,
}

export type IconProps = {
	type: 'image' | 'icon'
	image: SanityImage,
	icon: { svg: string }
	title: string
}

export type TabItemProps = {
	title: string,
	content: ContentHandlerProps
}

export type ProductProps = {
	title: string,
	description: PortableTextBlock[],
	cta: string,
	ctaIcon: { svg: string },
	ctaLink: string,
	highlights: PortableTextBlock[],
	image: SanityImage
}

export type ListItemProps = {
	title: string,
	description: PortableTextBlock[]
}

export type IconCardProps = {
	type: 'image' | 'icon',
	icon: { svg: string }
	image: SanityImage,
	title: string,
	description: PortableTextBlock[]
}

export type ColumnProps = {
	splitRatio: string,
	sticky: 'none' | 'left top' | 'right top' | 'left bottom' | 'right bottom',
	contentLeft: ContentHandlerProps,
	contentRight: ContentHandlerProps,
	reverseOrderOnMobile: boolean,
}

export type Office = {
	name: string,
	description: string,
	address: string,
	email: string;
	phones: string[],
	fax: string;
	location: {
		lat: number,
		lng: number
	}
}

export type Employee = {
	name: string;
	position: string;
	profile: PortableTextBlock[];
	image: SanityImage;
}

export type TimelineItemProps = {
	title: string;
	date: string;
	description: PortableTextBlock[];
}

export type FormField = {
	name: string;
	label: string;
	type: "text" | "number" | "email" | "password" | "textarea" | "select" | "checkbox" | "radio" | "file";
	options?: string[]; // For select or radio fields
	required?: boolean;
	placeholder?: string;
}

export type DynamicFormProps = {
	title: string;
    to: string;
    cc: string;
    subject: string;
    autoresponse: string;
	formFields: FormField[];
}

export type BlogProps = {
    date: string;
    title: string;
    slug: { current: string };
    seo: SEOData;
    image: SanityImage;
    content: PortableTextBlock[];
}

export type NewsCardProps = {
    title: string;
    date: string;
    type: string;
    image: SanityImage;
    link: string;
}