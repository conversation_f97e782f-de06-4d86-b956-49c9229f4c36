import { SEO } from "@/components/layout/seo";
import HeroSection from "@/components/sections/hero";
import { pageQueryResult } from "@/components/sections/sanityTypes";
import Section from "@/components/sections/section";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { Typography } from "@/components/ui/typography";
import { graphql, HeadFC, PageProps } from "gatsby";
import { Mail, Phone } from "lucide-react";
// import React from "react";
// import { router } from "next/router";
import React, { useState, ChangeEvent, FormEvent } from "react";
// import { Input } from "@/components/ui/input";
// import { Textarea } from "@/components/ui/textarea";
// import { Button } from "@/components/ui/button";

interface FormData {
  name: string;
  email: string;
  message: string;
}


interface ApiResponse {
  type: "success" | "error";
  message: string;
}


export default function ContactForm() {
  const [formData, setFormData] = useState<FormData>({
    name: "",
    email: "",
    message: "",
  });

  const [response, setResponse] = useState<ApiResponse | null>(null);
  const [loading, setLoading] = useState(false);

  const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setResponse(null);

    try {
      const res = await fetch("https://sahibnkmitradevapiapp1.banksekure.com/MBSWebsite/v1/MBSMailAPI/SendEmail", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      const text = await res.text(); // In case response is not JSON
      console.log("Raw response:", text);

      if (!res.ok) {
        throw new Error(`Error: ${res.status} ${res.statusText}`);
      }

      setResponse({ type: "success", message: "Message sent successfully!" });
      setFormData({ name: "", email: "", message: "" });
    } catch (err: any) {
      setResponse({ type: "error", message: err.message });
    } finally {
      setLoading(false);
    }
  };


// const Page: React.FC<PageProps<pageQueryResult>> = ({ data }) => {
// 	const hero = data.allSanityPage.nodes[0].hero;
	return (
		<div className="w-full">
			{/* <HeroSection {...hero} /> */}

			<Section id="get-in-touch" title="" subtitle="" statistics={[]} last>
				<div className="flex flex-col lg:flex-row gap-8">
					<Card className="lg:w-1/2">
						{/* <form method="POST" action={process.env.GATSBY_FORM_ENDPOINT}>
							<input type="hidden" name="type" value="Get in touch" />
							<CardHeader>
								<CardTitle>
									<Typography component="h3" size="xxl">
										Hi, We'd love to hear from you.
									</Typography>
								</CardTitle>
							</CardHeader>
							<CardContent className="flex flex-col gap-4">
								<Input placeholder="Name" name="name" />
								<Input placeholder="Email" name="email" type="email" />
								<Textarea placeholder="Message" name="message" />
							</CardContent>
							<CardFooter>
								<Button type="submit" variant="secondary" size="sm" className="w-full">
									Send Enquiry
								</Button>
							</CardFooter>
						</form> */}
						
						<form onSubmit={handleSubmit} className="space-y-4 max-w-md mx-auto">
							<CardHeader>
								<CardTitle>
									<Typography component="h3" size="xxl">
										Hi, We'd love to hear from you.
									</Typography>
								</CardTitle>
							</CardHeader>
							<CardContent className="flex flex-col gap-4">
						<Input
							placeholder="Name"
							name="name"
							value={formData.name}
							onChange={handleChange}
							required
						/>
						<Input
							placeholder="Email"
							name="email"
							type="email"
							value={formData.email}
							onChange={handleChange}
							required
						/>
						<Textarea
							placeholder="Message"
							name="message"
							value={formData.message}
							onChange={handleChange}
							required
						/>
						</CardContent>
					    <CardFooter>
						<Button type="submit" disabled={loading}>
							{loading ? "Sending..." : "Send Message"}
						</Button>
					  </CardFooter>
						{response && (
							<p
							className={`text-sm ${
								response.type === "success" ? "text-green-600" : "text-red-600"
							}`}
							>
							{response.message}
							</p>
						)}
						</form>
					</Card>
					<Card className="lg:w-1/2 p-6">
						<div>
							<Typography component="h4" size="xl">
								Registered Office
							</Typography>
							<Typography className="mt-2">Manipal Business Solutions Private Limited, Udayavani Building, Press Corner, Manipal – 576104, Karnataka</Typography>
							<Typography className="mt-2 text-secondary">
								<a href="tel:+91 820 2205000" className="flex gap-2">
									<Phone /> +91 820 2205000
								</a>
							</Typography>
							<Typography className="mt-2 text-secondary break-all">
								<a href="mailto:<EMAIL>" className="flex gap-2">
									<Mail />
									<EMAIL>
								</a>
							</Typography>
						</div>
						<Separator className="my-4" />
						<div>
							<Typography component="h4" size="xl">
								Corporate Office
							</Typography>
							<Typography className="mt-2">
								Manipal Business Solutions Private Limited, International Home Deco Park (“IHDP”), Plot No. 7, 5th Floor, Sector 127, Taj Express Way, Noida, Uttar Pradesh - 201 301
							</Typography>
							<Typography className="mt-2 text-secondary">
								<a href="tel:+91 124 4020030" className="flex gap-2">
									<Phone />
									+91 124 4020030
								</a>
							</Typography>
							<Typography className="mt-2 text-secondary break-all">
								<a href="mailto:<EMAIL>" className="flex gap-2">
									<Mail />
									<EMAIL>
								</a>
							</Typography>
						</div>
					</Card>
				</div>
			</Section>
		</div>
	);
};


export const Head: HeadFC<pageQueryResult> = ({ data }) => <SEO seo={data.allSanityPage.nodes[0].seo} />;

export const query = graphql`
	query PageQuery {
		allSanityPage(filter: { slug: { current: { eq: "get-in-touch" } } }) {
			nodes {
				title
				seo {
					title
					description
					keywords
					canonicalUrl
					ogTitle
					ogDescription
					ogImage {
						asset {
							publicUrl
							url
						}
					}
					noindex
					structuredData
				}
				hero {
					title
					subtitle
					cta
					ctaIcon {
						svg
					}
					ctaLink
					bgImage {
						asset {
							gatsbyImageData
							url
							publicUrl
						}
						alt
						title
					}
					imageAlign
				}
				sections {
					title
					subtitle
					id
					bg
					statistics {
						titleString
						titleNumber
						subtitle
						type
						icon {
							svg
						}
						image {
							asset {
								gatsbyImageData
								url
								publicUrl
							}
							alt
							title
						}
					}
					content {
						type
						content {
							title
							content: _rawContent
							cta
							ctaIcon {
								svg
							}
							ctaLink
							image {
								asset {
									gatsbyImageData
									url
									publicUrl
								}
								alt
								title
							}
							imageAlign
							imageAsBg
						}
						timeline {
							title
							date
							description: _rawDescription
						}
						bigList {
							title
							description: _rawDescription
						}
						carousel {
							type
							delay
							contentType
							iconCards {
								type
								image {
									asset {
										gatsbyImageData
										url
										publicUrl
									}
									alt
									title
								}
								icon {
									svg
								}
								title
								description: _rawDescription
							}
							carousel {
								title
								content: _rawContent
								cta
								ctaIcon {
									svg
								}
								ctaLink
								image {
									asset {
										gatsbyImageData
										url
										publicUrl
									}
									alt
									title
								}
								imageAlign
								imageAsBg
							}
						}
						iconCards {
							title
							type
							icon {
								svg
							}
							image {
								asset {
									gatsbyImageData
									url
									publicUrl
								}
								alt
								title
							}
							description: _rawDescription
						}
						icons {
							title
							type
							icon {
								svg
							}
							image {
								asset {
									gatsbyImageData
									url
									publicUrl
								}
								alt
								title
							}
						}
						offices {
							name
							description
							address
							phones
							fax
							email
							location {
								lat
								lng
							}
						}
						employees {
							name
							position
							profile: _rawProfile
							image {
								asset {
									gatsbyImageData
									url
									publicUrl
								}
								alt
								title
							}
						}
						product {
							title
							cta
							ctaLink
							image {
								asset {
									gatsbyImageData
									url
									publicUrl
								}
								alt
								title
							}
							highlights: _rawHighlights
							description: _rawDescription
						}
						tabs {
							title
							content {
								type
								content {
									title
									content: _rawContent
									cta
									ctaIcon {
										svg
									}
									ctaLink
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									imageAlign
									imageAsBg
								}
								timeline {
									title
									date
									description: _rawDescription
								}
								bigList {
									title
									description: _rawDescription
								}
								carousel {
									type
									delay
									contentType
									iconCards {
										type
										image {
											asset {
												gatsbyImageData
												url
												publicUrl
											}
											alt
											title
										}
										icon {
											svg
										}
										title
										description: _rawDescription
									}
									carousel {
										title
										content: _rawContent
										cta
										ctaIcon {
											svg
										}
										ctaLink
										image {
											asset {
												gatsbyImageData
												url
												publicUrl
											}
											alt
											title
										}
										imageAlign
										imageAsBg
									}
								}
								iconCards {
									title
									type
									icon {
										svg
									}
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									description: _rawDescription
								}
								icons {
									title
									type
									icon {
										svg
									}
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
								}
								product {
									title
									cta
									ctaLink
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									highlights: _rawHighlights
									description: _rawDescription
								}
								columns {
									splitRatio
									sticky
									contentLeft {
										type
										content {
											title
											content: _rawContent
											cta
											ctaIcon {
												svg
											}
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											imageAlign
											imageAsBg
										}
										timeline {
											title
											date
											description: _rawDescription
										}
										bigList {
											title
											description: _rawDescription
										}
										carousel {
											type
											delay
											contentType
											iconCards {
												type
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												icon {
													svg
												}
												title
												description: _rawDescription
											}
											carousel {
												title
												content: _rawContent
												cta
												ctaIcon {
													svg
												}
												ctaLink
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												imageAlign
												imageAsBg
											}
										}
										iconCards {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											description: _rawDescription
										}
										icons {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
										}
										product {
											title
											cta
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											highlights: _rawHighlights
											description: _rawDescription
										}
									}
									contentRight {
										type
										content {
											title
											content: _rawContent
											cta
											ctaIcon {
												svg
											}
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											imageAlign
											imageAsBg
										}
										timeline {
											title
											date
											description: _rawDescription
										}
										bigList {
											title
											description: _rawDescription
										}
										carousel {
											type
											delay
											contentType
											iconCards {
												type
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												icon {
													svg
												}
												title
												description: _rawDescription
											}
											carousel {
												title
												content: _rawContent
												cta
												ctaIcon {
													svg
												}
												ctaLink
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												imageAlign
												imageAsBg
											}
										}
										iconCards {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											description: _rawDescription
										}
										icons {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
										}
										product {
											title
											cta
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											highlights: _rawHighlights
											description: _rawDescription
										}
									}
								}
							}
						}
						columns {
							splitRatio
							sticky
							contentLeft {
								type
								content {
									title
									content: _rawContent
									cta
									ctaIcon {
										svg
									}
									ctaLink
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									imageAlign
									imageAsBg
								}
								timeline {
									title
									date
									description: _rawDescription
								}
								bigList {
									title
									description: _rawDescription
								}
								carousel {
									type
									delay
									contentType
									iconCards {
										type
										image {
											asset {
												gatsbyImageData
												url
												publicUrl
											}
											alt
											title
										}
										icon {
											svg
										}
										title
										description: _rawDescription
									}
									carousel {
										title
										content: _rawContent
										cta
										ctaIcon {
											svg
										}
										ctaLink
										image {
											asset {
												gatsbyImageData
												url
												publicUrl
											}
											alt
											title
										}
										imageAlign
										imageAsBg
									}
								}
								iconCards {
									title
									type
									icon {
										svg
									}
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									description: _rawDescription
								}
								icons {
									title
									type
									icon {
										svg
									}
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
								}
								product {
									title
									cta
									ctaLink
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									highlights: _rawHighlights
									description: _rawDescription
								}
								tabs {
									title
									content {
										type
										content {
											title
											content: _rawContent
											cta
											ctaIcon {
												svg
											}
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											imageAlign
											imageAsBg
										}
										timeline {
											title
											date
											description: _rawDescription
										}
										bigList {
											title
											description: _rawDescription
										}
										carousel {
											type
											delay
											contentType
											iconCards {
												type
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												icon {
													svg
												}
												title
												description: _rawDescription
											}
											carousel {
												title
												content: _rawContent
												cta
												ctaIcon {
													svg
												}
												ctaLink
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												imageAlign
												imageAsBg
											}
										}
										iconCards {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											description: _rawDescription
										}
										icons {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
										}
										product {
											title
											cta
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											highlights: _rawHighlights
											description: _rawDescription
										}
									}
								}
							}
							contentRight {
								type
								content {
									title
									content: _rawContent
									cta
									ctaIcon {
										svg
									}
									ctaLink
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									imageAlign
									imageAsBg
								}
								timeline {
									title
									date
									description: _rawDescription
								}
								bigList {
									title
									description: _rawDescription
								}
								carousel {
									type
									delay
									contentType
									iconCards {
										type
										image {
											asset {
												gatsbyImageData
												url
												publicUrl
											}
											alt
											title
										}
										icon {
											svg
										}
										title
										description: _rawDescription
									}
									carousel {
										title
										content: _rawContent
										cta
										ctaIcon {
											svg
										}
										ctaLink
										image {
											asset {
												gatsbyImageData
												url
												publicUrl
											}
											alt
											title
										}
										imageAlign
										imageAsBg
									}
								}
								iconCards {
									title
									type
									icon {
										svg
									}
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									description: _rawDescription
								}
								icons {
									title
									type
									icon {
										svg
									}
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
								}
								product {
									title
									cta
									ctaLink
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									highlights: _rawHighlights
									description: _rawDescription
								}
								tabs {
									title
									content {
										type
										content {
											title
											content: _rawContent
											cta
											ctaIcon {
												svg
											}
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											imageAlign
											imageAsBg
										}
										timeline {
											title
											date
											description: _rawDescription
										}
										bigList {
											title
											description: _rawDescription
										}
										carousel {
											type
											delay
											contentType
											iconCards {
												type
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												icon {
													svg
												}
												title
												description: _rawDescription
											}
											carousel {
												title
												content: _rawContent
												cta
												ctaIcon {
													svg
												}
												ctaLink
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												imageAlign
												imageAsBg
											}
										}
										iconCards {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											description: _rawDescription
										}
										icons {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
										}
										product {
											title
											cta
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											highlights: _rawHighlights
											description: _rawDescription
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}
`;