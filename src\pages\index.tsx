import { SEO } from "@/components/layout/seo";
import { pageQueryResult } from "@/components/sections/sanityTypes";
import Section from "@/components/sections/section";
import { SectionDivider, topCurvePath } from "@/components/sections/sectionDividers";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Typography } from "@/components/ui/typography";
import video from "@/images/homepage/main-banner.webm";
import { scrollDown, splitStringInRatio } from "@/lib/utils";
import { graphql, type HeadFC, type PageProps } from "gatsby";
import { ChevronRight } from "lucide-react";
import React from "react";
import ScrollAnimation from "react-animate-on-scroll";

const IndexPage: React.FC<PageProps<pageQueryResult>> = ({ data }) => {
	const page = data.allSanityPage.nodes[0];
	const { part1, part2 } = splitStringInRatio(page.hero.title);

	return (
		<div className="w-full">
			{/* Hero image */}
			<section className="h-screen hero  flex flex-col justify-center items-center overflow-hidden bg-black">
				{/* Video */}
				<div className="absolute h-full w-full translate-x-1/4">
					<video autoPlay muted loop playsInline preload="metadata" className="h-full w-full object-cover object-left filter brightness-75">
						<source src={video} type="video/webm" />
					</video>
				</div>

				<div className="h-full flex items-center">
					<div className="lg:w-2/3 px-5 md:px-10 text-muted drop-shadow-outline">
						<ScrollAnimation animateIn="animate-in fade-in slide-in-from-bottom" duration={0.5} animateOnce>
							<Typography component="h1" size="xl4" weight="bold" className="mb-2">
								{part1}
								<br />
								<span className="text-secondary">{part2}</span>
							</Typography>
						</ScrollAnimation>
						<ScrollAnimation animateIn="animate-in fade-in slide-in-from-bottom" duration={0.5} delay={250} animateOnce>
							<Typography size="subh1" weight="light" component="h2">
								{page.hero.subtitle}
							</Typography>
						</ScrollAnimation>

						<div className="flex gap-4 my-5 md:my-10">
							<ScrollAnimation animateIn="animate-in fade-in slide-in-from-bottom" duration={0.5} delay={500} animateOnce>
								<Button onClick={scrollDown} className="hover:text-primary-foreground">
									Get Started
									<ChevronRight className="size-5" />
								</Button>
							</ScrollAnimation>
							{/* <ScrollAnimation animateIn="animate-in fade-in slide-in-from-bottom" duration={0.5} delay={750} animateOnce>
                                <Link to="/book-demo">
                                    <Button variant='outline'>
                                        Book A Demo
                                        <ChevronRight className="size-5" />
                                    </Button>
                                </Link>
                            </ScrollAnimation> */}
						</div>
					</div>

					{/* <div className="lg:flex hidden p-10 h-full max-w-1/3 justify-center items-center">
                        <StaticImage src="../images/homepage/home-banner-one-2.webp" alt="Banner" className="m-auto w-full max-h-full max-w-full aspect-square rounded-full shadow-xl" />
                    </div> */}
				</div>

				<div className={`w-full absolute bottom-0 -z-10 translate-y-1/2`}>
					<svg id="bottomWave" xmlns="http://www.w3.org/2000/svg" className={`w-full block text-background`} viewBox="0 0 1440 224" preserveAspectRatio="none">
						<path fill="currentColor" fillOpacity="1" d={topCurvePath} />
					</svg>
				</div>
			</section>

			{page.sections.map((section, i) => (
				<React.Fragment key={i}>
					<Section {...section} last={page.sections.length - 1 === i} />
					{page.sections.length - 1 !== i && section.bg !== "dark" && page.sections[i + 1].bg !== "dark" && <SectionDivider />}
				</React.Fragment>
			))}
		</div>
	);
};

export default IndexPage;

export const Head: HeadFC<pageQueryResult> = ({ data }) => <SEO seo={data.allSanityPage.nodes[0].seo} />;

export const query = graphql`
	query PageQuery {
		allSanityPage(filter: { slug: { current: { eq: "home" } } }) {
			nodes {
				title
				seo {
					title
					description
					keywords
					canonicalUrl
					ogTitle
					ogDescription
					ogImage {
						asset {
							publicUrl
							url
						}
					}
					noindex
					structuredData
				}
				hero {
					title
					subtitle
					cta
					ctaIcon {
						svg
					}
					ctaLink
					bgImage {
						asset {
							gatsbyImageData
							url
							publicUrl
						}
						alt
						title
					}
					imageAlign
				}
				sections {
					title
					subtitle
					id
					bg
					statistics {
						titleString
						titleNumber
						subtitle
						type
						icon {
							svg
						}
						image {
							asset {
								gatsbyImageData
								url
								publicUrl
							}
							alt
							title
						}
					}
					content {
						type
						content {
							title
							content: _rawContent
							cta
							ctaIcon {
								svg
							}
							ctaLink
							image {
								asset {
									gatsbyImageData
									url
									publicUrl
								}
								alt
								title
							}
							imageAlign
							imageAsBg
						}
						timeline {
							title
							date
							description: _rawDescription
						}
						bigList {
							title
							description: _rawDescription
						}
						carousel {
							type
							delay
							contentType
							iconCards {
								type
								image {
									asset {
										gatsbyImageData
										url
										publicUrl
									}
									alt
									title
								}
								icon {
									svg
								}
								title
								description: _rawDescription
							}
							carousel {
								title
								content: _rawContent
								cta
								ctaIcon {
									svg
								}
								ctaLink
								image {
									asset {
										gatsbyImageData
										url
										publicUrl
									}
									alt
									title
								}
								imageAlign
								imageAsBg
							}
						}
						iconCards {
							title
							type
							icon {
								svg
							}
							image {
								asset {
									gatsbyImageData
									url
									publicUrl
								}
								alt
								title
							}
							description: _rawDescription
						}
						icons {
							title
							type
							icon {
								svg
							}
							image {
								asset {
									gatsbyImageData
									url
									publicUrl
								}
								alt
								title
							}
						}
						offices {
							name
							description
							address
							phones
							fax
							email
							location {
								lat
								lng
							}
						}
						employees {
							name
							position
							profile: _rawProfile
							image {
								asset {
									gatsbyImageData
									url
									publicUrl
								}
								alt
								title
							}
						}
						product {
							title
							cta
							ctaLink
							image {
								asset {
									gatsbyImageData
									url
									publicUrl
								}
								alt
								title
							}
							highlights: _rawHighlights
							description: _rawDescription
						}
						tabs {
							title
							content {
								type
								content {
									title
									content: _rawContent
									cta
									ctaIcon {
										svg
									}
									ctaLink
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									imageAlign
									imageAsBg
								}
								timeline {
									title
									date
									description: _rawDescription
								}
								bigList {
									title
									description: _rawDescription
								}
								carousel {
									type
									delay
									contentType
									iconCards {
										type
										image {
											asset {
												gatsbyImageData
												url
												publicUrl
											}
											alt
											title
										}
										icon {
											svg
										}
										title
										description: _rawDescription
									}
									carousel {
										title
										content: _rawContent
										cta
										ctaIcon {
											svg
										}
										ctaLink
										image {
											asset {
												gatsbyImageData
												url
												publicUrl
											}
											alt
											title
										}
										imageAlign
										imageAsBg
									}
								}
								iconCards {
									title
									type
									icon {
										svg
									}
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									description: _rawDescription
								}
								icons {
									title
									type
									icon {
										svg
									}
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
								}
								product {
									title
									cta
									ctaLink
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									highlights: _rawHighlights
									description: _rawDescription
								}
								columns {
									splitRatio
									sticky
									contentLeft {
										type
										content {
											title
											content: _rawContent
											cta
											ctaIcon {
												svg
											}
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											imageAlign
											imageAsBg
										}
										timeline {
											title
											date
											description: _rawDescription
										}
										bigList {
											title
											description: _rawDescription
										}
										carousel {
											type
											delay
											contentType
											iconCards {
												type
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												icon {
													svg
												}
												title
												description: _rawDescription
											}
											carousel {
												title
												content: _rawContent
												cta
												ctaIcon {
													svg
												}
												ctaLink
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												imageAlign
												imageAsBg
											}
										}
										iconCards {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											description: _rawDescription
										}
										icons {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
										}
										product {
											title
											cta
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											highlights: _rawHighlights
											description: _rawDescription
										}
									}
									contentRight {
										type
										content {
											title
											content: _rawContent
											cta
											ctaIcon {
												svg
											}
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											imageAlign
											imageAsBg
										}
										timeline {
											title
											date
											description: _rawDescription
										}
										bigList {
											title
											description: _rawDescription
										}
										carousel {
											type
											delay
											contentType
											iconCards {
												type
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												icon {
													svg
												}
												title
												description: _rawDescription
											}
											carousel {
												title
												content: _rawContent
												cta
												ctaIcon {
													svg
												}
												ctaLink
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												imageAlign
												imageAsBg
											}
										}
										iconCards {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											description: _rawDescription
										}
										icons {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
										}
										product {
											title
											cta
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											highlights: _rawHighlights
											description: _rawDescription
										}
									}
								}
							}
						}
						columns {
							splitRatio
							sticky
							contentLeft {
								type
								content {
									title
									content: _rawContent
									cta
									ctaIcon {
										svg
									}
									ctaLink
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									imageAlign
									imageAsBg
								}
								timeline {
									title
									date
									description: _rawDescription
								}
								bigList {
									title
									description: _rawDescription
								}
								carousel {
									type
									delay
									contentType
									iconCards {
										type
										image {
											asset {
												gatsbyImageData
												url
												publicUrl
											}
											alt
											title
										}
										icon {
											svg
										}
										title
										description: _rawDescription
									}
									carousel {
										title
										content: _rawContent
										cta
										ctaIcon {
											svg
										}
										ctaLink
										image {
											asset {
												gatsbyImageData
												url
												publicUrl
											}
											alt
											title
										}
										imageAlign
										imageAsBg
									}
								}
								iconCards {
									title
									type
									icon {
										svg
									}
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									description: _rawDescription
								}
								icons {
									title
									type
									icon {
										svg
									}
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
								}
								product {
									title
									cta
									ctaLink
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									highlights: _rawHighlights
									description: _rawDescription
								}
								tabs {
									title
									content {
										type
										content {
											title
											content: _rawContent
											cta
											ctaIcon {
												svg
											}
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											imageAlign
											imageAsBg
										}
										timeline {
											title
											date
											description: _rawDescription
										}
										bigList {
											title
											description: _rawDescription
										}
										carousel {
											type
											delay
											contentType
											iconCards {
												type
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												icon {
													svg
												}
												title
												description: _rawDescription
											}
											carousel {
												title
												content: _rawContent
												cta
												ctaIcon {
													svg
												}
												ctaLink
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												imageAlign
												imageAsBg
											}
										}
										iconCards {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											description: _rawDescription
										}
										icons {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
										}
										product {
											title
											cta
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											highlights: _rawHighlights
											description: _rawDescription
										}
									}
								}
							}
							contentRight {
								type
								content {
									title
									content: _rawContent
									cta
									ctaIcon {
										svg
									}
									ctaLink
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									imageAlign
									imageAsBg
								}
								timeline {
									title
									date
									description: _rawDescription
								}
								bigList {
									title
									description: _rawDescription
								}
								carousel {
									type
									delay
									contentType
									iconCards {
										type
										image {
											asset {
												gatsbyImageData
												url
												publicUrl
											}
											alt
											title
										}
										icon {
											svg
										}
										title
										description: _rawDescription
									}
									carousel {
										title
										content: _rawContent
										cta
										ctaIcon {
											svg
										}
										ctaLink
										image {
											asset {
												gatsbyImageData
												url
												publicUrl
											}
											alt
											title
										}
										imageAlign
										imageAsBg
									}
								}
								iconCards {
									title
									type
									icon {
										svg
									}
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									description: _rawDescription
								}
								icons {
									title
									type
									icon {
										svg
									}
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
								}
								product {
									title
									cta
									ctaLink
									image {
										asset {
											gatsbyImageData
											url
											publicUrl
										}
										alt
										title
									}
									highlights: _rawHighlights
									description: _rawDescription
								}
								tabs {
									title
									content {
										type
										content {
											title
											content: _rawContent
											cta
											ctaIcon {
												svg
											}
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											imageAlign
											imageAsBg
										}
										timeline {
											title
											date
											description: _rawDescription
										}
										bigList {
											title
											description: _rawDescription
										}
										carousel {
											type
											delay
											contentType
											iconCards {
												type
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												icon {
													svg
												}
												title
												description: _rawDescription
											}
											carousel {
												title
												content: _rawContent
												cta
												ctaIcon {
													svg
												}
												ctaLink
												image {
													asset {
														gatsbyImageData
														url
														publicUrl
													}
													alt
													title
												}
												imageAlign
												imageAsBg
											}
										}
										iconCards {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											description: _rawDescription
										}
										icons {
											title
											type
											icon {
												svg
											}
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
										}
										product {
											title
											cta
											ctaLink
											image {
												asset {
													gatsbyImageData
													url
													publicUrl
												}
												alt
												title
											}
											highlights: _rawHighlights
											description: _rawDescription
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}
`;
