@tailwind base;
@tailwind components;
@tailwind utilities;
@import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Geist:wght@100..900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");

html {
	scroll-behavior: smooth;
}

body {
	width: 100%;
	height: 100%;
	margin: 0;
	padding: 0;
}

a:focus-visible {
	outline: none;
}

button .button__blobs {
	height: 100%;
	filter: url(#goo);
	overflow: hidden;
	position: absolute;
	top: 0;
	left: 0;
	bottom: -3px;
	right: -1px;
	z-index: -1;
}

button .button__blobs div {
	width: 34%;
	height: 100%;
	border-radius: 100%;
	position: absolute;
	transition: transform 700ms ease, filter 650ms ease 400ms;
}

button .button__blobs div:nth-child(1) {
	left: -5%;
}

button .button__blobs div:nth-child(2) {
	left: 30%;
	transition-delay: 60ms, 400ms;
}

button .button__blobs div:nth-child(3) {
	left: 66%;
	transition-delay: 25ms, 400ms;
}

/* Debug ghost elements */
/* * {
    background: #000 !important;
    color: #0f0 !important;
    outline: solid #f00 1px !important;
} */

.list-decimal {
	list-style-type: decimal;
}

.text-outline {
	color: transparent;
	-webkit-text-stroke: 0.05rem hsl(var(--foreground));
}

.text-outline-accent {
	color: transparent;
	-webkit-text-stroke: 0.125rem hsl(var(--accent));
}

.text-outline-secondary {
	color: transparent;
	-webkit-text-stroke: 0.125rem hsl(var(--secondary));
}

.text-outline-primary {
	color: transparent;
	-webkit-text-stroke: 0.125rem hsl(var(--primary));
}

/* Target WebKit browsers (Chrome, Safari, Edge) */
.scrollable {
	overflow: auto;
	scrollbar-width: thin; /* for Firefox */
	scrollbar-color: auto transparent; /* thumb color + track color */
}

/* For Chrome, Safari and Edge */
.scrollable::-webkit-scrollbar {
	width: 8px;
	height: 8px;
}

.scrollable::-webkit-scrollbar-track {
	background: transparent;
}

.scrollable::-webkit-scrollbar-thumb {
	background-color: rgba(100, 100, 100, 0.6); /* Customize thumb color */
	border-radius: 4px;
}

@layer base {
	* {
		@apply border-border;
	}

	body {
		@apply bg-background text-foreground;
	}

	main {
		@apply isolate;
	}

	html {
		font-family: Poppins, sans-serif;
	}
}
