import DefaultContent from "@/components/sections/content";
import { IconCardItem } from "@/components/sections/iconCards";
import { CarouselProps } from "@/components/sections/sanityTypes";
import { useSection } from "@/components/sections/section";
import { Carousel, CarouselContent, CarouselIndicator, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel";
import ScrollAnim from "@/components/ui/scrollAnim";
import { GatsbyImage } from "gatsby-plugin-image";
import React, { useEffect, useState } from "react";

const CarouselSection: React.FC<CarouselProps> = ({ type, delay, carousel, iconCards, contentType, autoscroll }: CarouselProps) => {
	// Ensures that the carousel autoplay is only rendered client-side
	const [isClient, setIsClient] = useState(false);
	useEffect(() => {
		setIsClient(true);
	}, []);
	const Autoplay = isClient ? require("embla-carousel-autoplay") : null;
	const Autoscroll = isClient ? require("embla-carousel-auto-scroll") : null;

	const { bg } = useSection();

	const basis =
		type === "Full"
			? "basis-full"
			: type === "Half"
			? "basis-full md:basis-1/2"
			: type === "Third"
			? "md:basis-1/2 lg:basis-1/3"
			: type === "Quarter"
			? "basis-1/2 md:basis-1/3 2xl:basis-1/4"
			: "basis-52";

	const hideIndicator = () => {
		switch (type) {
			case "Half":
				return {
					"2xl": 1,
					lg: 1,
					md: 1,
					default: 0,
				};
			case "Third":
				return {
					"2xl": 2,
					lg: 2,
					md: 1,
					default: 0,
				};
			case "Quarter":
				return {
					"2xl": 3,
					lg: 2,
					md: 2,
					default: 1,
				};
			default:
				return {
					"2xl": 0,
					lg: 0,
					md: 0,
					default: 0,
				};
		}
	};

	const contentLength = contentType === "iconCard" ? iconCards.length : carousel.length;

	const indicatorHider = () => {
		if (contentLength <= 1) return "hidden";
		else if (type === "Half" && contentLength <= 2) return "flex md:hidden";
		else if (type === "Third") {
			if (contentLength <= 2) return "flex md:hidden";
			else if (contentLength <= 3) return "flex lg:hidden";
		} else if (type === "Quarter") {
			if (contentLength <= 2) return "hidden";
			else if (contentLength <= 3) return "flex md:hidden";
			else if (contentLength <= 4) return "flex 2xl:hidden";
		}
		return "";
	};

	const CarouselWrapper = ({ children }: { children: React.ReactNode }) => {
		if (type === "Mini") return <div className="container mx-auto px-10">{children}</div>;
		else return <>{children}</>;
	};

	return (
		<CarouselWrapper>
			<ScrollAnim noAnim={type !== "Mini"}>
				<Carousel
					className={`${type === "Mini" ? "mx-4" : ""}`}
					plugins={isClient ? (autoscroll ? [Autoscroll()] : [Autoplay({ delay: delay ?? 5000 })]) : []}
					opts={{ loop: type === "Mini" }}
				>
					<CarouselContent>
						{contentType === "iconCard"
							? iconCards.map((item, index) => (
									<CarouselItem key={index} className="basis-full md:basis-1/2 lg:basis-1/4">
										<div className="p-4 h-full">
											<IconCardItem {...item} minify={false} />
										</div>
									</CarouselItem>
							  ))
							: carousel.map((item, index) => (
									<CarouselItem key={index} className={basis}>
										{type === "Mini" ? (
											<div className="flex justify-center items-center h-full">
												<GatsbyImage
													image={item.image.asset.gatsbyImageData}
													alt={item.image?.alt || item.title || "Company Logo"}
													title={item.image?.title || item.title || "Company Logo"}
													imgStyle={{
														width: 150,
														height: "auto",
														maxHeight: 80,
														margin: "auto",
													}}
													objectFit="contain"
												/>
											</div>
										) : (
											<div className="lg:p-4 lg:pt-0 h-full">
												<DefaultContent key={index} {...item} stacked={type !== "Full"} />
											</div>
										)}
									</CarouselItem>
							  ))}
					</CarouselContent>
					{type === "Mini" ? (
						<>
							<CarouselNext />
							<CarouselPrevious />
						</>
					) : (
						!autoscroll && <CarouselIndicator variant="primary" hide={hideIndicator()} className={`mt-4 ${bg === "dark" ? "px-10" : "px-4"} mx-auto justify-center ${indicatorHider()}`} />
					)}
				</Carousel>
			</ScrollAnim>
		</CarouselWrapper>
	);
};

export default CarouselSection;
