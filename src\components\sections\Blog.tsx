import React from "react";
import { BlogProps } from "./sanityTypes";
import sanitySerializers from "@/lib/sanity";
import { PortableText } from "@portabletext/react";
import { GatsbyImage } from "gatsby-plugin-image";
import { Typography } from "../ui/typography";
import { Button } from "../ui/button";
import { ArrowLeftCircle } from "lucide-react";
import { Link } from "gatsby";

const BlogComponent: React.FC<BlogProps> = ({ title, image, content, date }) => {
    const imageAlt = image?.alt || title || 'image'
    const imageTitle = image?.title || title || 'image'

    const BackButton = ({ className }: { className: string }) => (
        <Link to="/blogs" className={className}>
            <ArrowLeftCircle className="text-primary size-8"/>
        </Link>
    )

    return (
        <div className="w-full p-5 pt-28 md:p-10 md:pt-32 relative">
            <BackButton className="hidden md:block fixed bg-background rounded-full top-0 left-0 mt-24 ml-1" />
            <Typography component='h1' size='xl3' className="mb-8">
                {title}
            </Typography>
            <div className="w-1/2 float-right ml-4">
                <GatsbyImage
                    image={image.asset.gatsbyImageData}
                    alt={imageAlt}
                    title={imageTitle}
                    objectFit="contain"
                />
            </div>
            {content && <PortableText value={content} components={sanitySerializers("mb-4")} />}
        </div>
    )
}

export default BlogComponent;