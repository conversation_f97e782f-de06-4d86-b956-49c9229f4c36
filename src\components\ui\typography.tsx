import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const typographyVariants = cva(
    "",
    {
        variants: {
            size: {
                default: "text-base",
                xl4: "scroll-m-20 tracking-tight font-extrabold text-6xl leading-[1.1]",
                xl3: "scroll-m-20 tracking-tight font-bold text-4xl md:text-5xl",
                xxl: "scroll-m-20 tracking-tight font-semibold text-3xl lg:text-4xl",
                xl: "scroll-m-20 tracking-tight font-semibold text-xl md:text-2xl",
                subh1: "scroll-m-20 text-xl md:text-2xl xl:text-3xl",
                lg: "scroll-m-20 text-lg md:text-xl xl:text-2xl",
                sm: "text-sm",
            },
            weight: {
                default: "",
                extralight: "font-extralight",
                light: "font-light",
                medium: "font-medium",
                semibold: "font-semibold",
                bold: "font-bold",
                extrabold: "font-extrabold"
            },
        },
        defaultVariants: {
            size: 'default',
            weight: 'default',
        }
    }
)

export interface TypographyProps
    extends React.HTMLAttributes<HTMLElement>,
    VariantProps<typeof typographyVariants> {
    component?: React.ElementType
}

const Typography = React.forwardRef<HTMLElement, TypographyProps>(
    ({ className, size, weight, component: Component = 'p', ...props }, ref) => {
        let variant = cn(typographyVariants({ size, weight, className }));
        if (Component === 'li')
            variant = cn(variant, className, "marker:text-secondary mb-2")

        return <Component className={variant} ref={ref} {...props} />
    }
)

export { Typography, typographyVariants }