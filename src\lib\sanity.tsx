import { Typography } from "@/components/ui/typography";
import { PortableTextComponents } from "@portabletext/react";
import React from "react";

const sanitySerializers = (className?: string) => {
    const serial: PortableTextComponents = {
        // Block styles (e.g., normal text, headings)
        block: ({ children, value }) => {
            const { style } = value;
            switch (style) {
                case "h1":
                    return <Typography component="h1" size='xl4' className={className}>{children}</Typography>;
                case "h2":
                    return <Typography component="h2" size='xl3'className={className}>{children}</Typography>;
                case "h3":
                    return <Typography component="h3" size='xxl' className={className}>{children}</Typography>;
                case "h4":
                    return <Typography component="h4" size='xl' className={className}>{children}</Typography>;
                case "h5":
                    return <Typography component="h5" size='subh1' className={className}>{children}</Typography>;
                case "h6":
                    return <Typography component="h6" size='lg' className={className}>{children}</Typography>;
                default:
                    return <Typography className={className}>{children}</Typography>;
            }
        },

        // List items
        listItem: ({ children }) => {
            return <Typography component='li' className={className}>{children}</Typography>;
        },

        // Ordered and unordered lists
        list: {
            bullet: ({ children }) => <ul className="pl-4 list-disc">{children}</ul>,
            number: ({ children }) => <ol className="pl-4 list-decimal">{children}</ol>,
        },

        // Marks (e.g., bold, italic, links)
        marks: {
            strong: ({ children }) => (
                <span className="font-bold">
                    {children}
                </span>
            ),
            em: ({ children }) => (
                <span className="italic font-light">
                    {children}
                </span>
            ),
            link: ({ children, value }) => (
                <a
                    href={value.href}
                    target={value.blank ? "_blank" : "_self"}
                    rel="noopener noreferrer"
                    className="text-secondary underline"
                >
                    {children}
                </a>
            ),
        },
    }

    return serial;
};

export default sanitySerializers;
